/**
 * Experts Service
 * Handles database operations for experts
 */

const { pool, sql } = require('../../config/db');
const logger = require('../../utils/logger');

/**
 * Get all experts
 * @returns {Promise<Array>} - List of experts
 */
const getAllExperts = async () => {
  try {
    const result = await pool.request().query(`
      SELECT
        e.ExpertID as id,
        u.UserID,
        u.Username,
        u.Email,
        u.FirstName as firstName,
        u.LastName as lastName,
        u.PhoneNumber,
        u.IsActive,
        e.Specialty as specialty,
        e.Biography as biography,
        e.Education as education,
        e.Certificates as certificates,
        e.ShortBio as shortBio,
        e.ExperienceYears as experienceYears,
        e.HourlyRate as hourlyRate,
        e.IsVerified,
        e.Rating as rating,
        e.ReviewCount as reviewCount,
        e.ProfileImage as profileImage
      FROM
        dbo.Experts e
      JOIN
        dbo.Users u ON e.UserID = u.UserID
      WHERE
        u.IsActive = 1
        AND u.IsEmailVerified = 1
    `);

    const experts = result.recordset;

    // Her expert için specialties'i yükle
    for (let expert of experts) {
      expert.specialties = await getExpertSpecialties(expert.id);
    }

    return experts;
  } catch (error) {
    logger.error('Error getting all experts:', error);
    throw error;
  }
};

/**
 * Get expert by ID
 * @param {number} expertId - Expert ID
 * @returns {Promise<object|null>} - Expert object or null
 */
const getExpertById = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);
    
    const result = await request.query(`
      SELECT
        e.ExpertID,
        u.UserID,
        u.Username,
        u.Email,
        u.FirstName,
        u.LastName,
        u.PhoneNumber,
        u.IsActive,
        e.Specialty,
        e.Biography,
        e.Education,
        e.Certificates,
        e.ShortBio,
        e.ExperienceYears,
        e.HourlyRate,
        e.IsVerified,
        e.Rating,
        e.ReviewCount,
        e.ProfileImage,
        e.ResponseTime,
        e.SessionDuration,
        e.LocationCity,
        e.LocationCountry
      FROM
        dbo.Experts e
      JOIN
        dbo.Users u ON e.UserID = u.UserID
      WHERE
        e.ExpertID = @ExpertID
    `);
    
    return result.recordset[0] || null;
  } catch (error) {
    logger.error('Error getting expert by ID:', error);
    throw error;
  }
};

/**
 * Get expert by user ID
 * @param {number} userId - User ID
 * @returns {Promise<object|null>} - Expert object or null
 */
const getExpertByUserId = async (userId) => {
  try {
    const request = pool.request();
    request.input('UserID', sql.Int, userId);
    
    const result = await request.query(`
      SELECT
        e.ExpertID,
        u.UserID,
        u.Username,
        u.Email,
        u.FirstName,
        u.LastName,
        u.PhoneNumber,
        u.IsActive,
        e.Specialty,
        e.Biography,
        e.Education,
        e.Certificates,
        e.ShortBio,
        e.ExperienceYears,
        e.HourlyRate,
        e.IsVerified,
        e.Rating,
        e.ReviewCount,
        e.ProfileVisibility,
        e.ResponseTime,
        e.SessionDuration,
        e.ProfileImage,
        e.VideoIntro,
        e.LocationAddress,
        e.LocationCity,
        e.LocationCountry
      FROM
        dbo.Experts e
      JOIN
        dbo.Users u ON e.UserID = u.UserID
      WHERE
        e.UserID = @UserID
    `);
    
    return result.recordset[0] || null;
  } catch (error) {
    logger.error('Error getting expert by user ID:', error);
    throw error;
  }
};

/**
 * Update expert profile
 * @param {number} expertId - Expert ID
 * @param {object} expertData - Expert data to update
 * @returns {Promise<boolean>} - True if successful
 */
const updateExpertProfile = async (expertId, expertData) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);
    request.input('Specialty', sql.NVarChar(100), expertData.specialty || null);
    request.input('Biography', sql.NVarChar(sql.MAX), expertData.biography || null);
    request.input('Education', sql.NVarChar(sql.MAX), expertData.education || null);
    request.input('Certificates', sql.NVarChar(sql.MAX), expertData.certificates || null);
    request.input('ShortBio', sql.NVarChar(500), expertData.shortBio || null);
    request.input('ExperienceYears', sql.Int, expertData.experienceYears ? parseInt(expertData.experienceYears) : null);
    request.input('HourlyRate', sql.Decimal(10, 2), expertData.hourlyRate ? parseFloat(expertData.hourlyRate) : null);
    
    // Yeni alanlar için input parametrelerini ekle
    request.input('ProfileVisibility', sql.Bit, expertData.profileVisibility !== undefined ? expertData.profileVisibility : true);
    request.input('ResponseTime', sql.Int, expertData.responseTime ? parseInt(expertData.responseTime) : 24);
    request.input('SessionDuration', sql.Int, expertData.sessionDuration ? parseInt(expertData.sessionDuration) : 50);
    request.input('ProfileImage', sql.NVarChar(255), expertData.profileImage || null);
    request.input('VideoIntro', sql.NVarChar(255), expertData.videoIntro || null);
    request.input('LocationAddress', sql.NVarChar(255), expertData.locationAddress || null);
    request.input('LocationCity', sql.NVarChar(50), expertData.locationCity || null);
    request.input('LocationCountry', sql.NVarChar(50), expertData.locationCountry || null);
    
    const result = await request.query(`
      UPDATE dbo.Experts
      SET 
        Specialty = @Specialty,
        Biography = @Biography,
        Education = @Education,
        Certificates = @Certificates,
        ShortBio = @ShortBio,
        ExperienceYears = @ExperienceYears,
        HourlyRate = @HourlyRate,
        ProfileVisibility = @ProfileVisibility,
        ResponseTime = @ResponseTime,
        SessionDuration = @SessionDuration,
        ProfileImage = @ProfileImage,
        VideoIntro = @VideoIntro,
        LocationAddress = @LocationAddress,
        LocationCity = @LocationCity,
        LocationCountry = @LocationCountry,
        UpdatedAt = GETDATE()
      WHERE 
        ExpertID = @ExpertID;
      
      SELECT @@ROWCOUNT AS AffectedRows;
    `);
    
    return result.recordset[0].AffectedRows > 0;
  } catch (error) {
    logger.error('Error updating expert profile:', error);
    throw error;
  }
};

/**
 * Get expert availability
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} - List of availability slots
 */
const getExpertAvailability = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);
    
    const result = await request.query(`
      SELECT 
        AvailabilityID,
        DayOfWeek,
        StartTime,
        EndTime,
        IsRecurring,
        SpecificDate
      FROM 
        dbo.ExpertAvailability
      WHERE 
        ExpertID = @ExpertID
      ORDER BY
        CASE WHEN SpecificDate IS NULL THEN 1 ELSE 0 END,
        SpecificDate,
        DayOfWeek,
        StartTime
    `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error getting expert availability:', error);
    throw error;
  }
};

/**
 * Add expert availability slot
 * @param {object} availabilityData - Availability data
 * @returns {Promise<number>} - New availability ID
 */
const addExpertAvailability = async (availabilityData) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, availabilityData.expertId);
    request.input('DayOfWeek', sql.Int, availabilityData.dayOfWeek);
    
    // SQL Server TIME tipini doğru şekilde işleme
    // Bu konuda emin olmak için, zaman değerlerinin tam olarak HH:MM:SS formatında olduğundan emin olalım
    const formatTimeString = (timeString) => {
      if (!timeString) {
        logger.warn('Null or undefined time string received');
        return null;
      }
      
      // Eğer HH:MM formatındaysa, saniye ekle
      if (/^\d{2}:\d{2}$/.test(timeString)) {
        logger.info(`Formatting time from '${timeString}' to '${timeString}:00'`);
        return `${timeString}:00`;
      }
      
      // Eğer zaten doğru formattaysa veya başka bir formattaysa kontrol et
      if (/^\d{2}:\d{2}:\d{2}$/.test(timeString)) {
        logger.info(`Time string already in correct format: '${timeString}'`);
      } else {
        logger.warn(`Unexpected time format: '${timeString}', attempting to use as is`);
      }
      
      return timeString;
    };
    
    const formattedStartTime = formatTimeString(availabilityData.startTime);
    const formattedEndTime = formatTimeString(availabilityData.endTime);
    
    logger.info('Formatted time values for insert:', {
      original: {
        startTime: availabilityData.startTime,
        endTime: availabilityData.endTime
      },
      formatted: {
        startTime: formattedStartTime,
        endTime: formattedEndTime
      }
    });
    
    // TIME değerlerini VarChar olarak gönder ve sonra SQL tarafında dönüştür
    request.input('StartTime', sql.VarChar(8), formattedStartTime);
    request.input('EndTime', sql.VarChar(8), formattedEndTime);
    
    const isRecurring = availabilityData.isRecurring !== undefined ? availabilityData.isRecurring : true;
    request.input('IsRecurring', sql.Bit, isRecurring);
    
    // SpecificDate null veya boş string olabilir, bunu kontrol et
    const specificDate = availabilityData.specificDate === "" ? null : availabilityData.specificDate;
    request.input('SpecificDate', sql.Date, specificDate);
    
    logger.info('Final values for SQL query:', {
      expertId: availabilityData.expertId,
      dayOfWeek: availabilityData.dayOfWeek,
      startTime: formattedStartTime,
      endTime: formattedEndTime,
      isRecurring: isRecurring,
      specificDate: specificDate
    });
    
    const result = await request.query(`
      INSERT INTO dbo.ExpertAvailability (
        ExpertID, DayOfWeek, StartTime, EndTime, 
        IsRecurring, SpecificDate, CreatedAt, UpdatedAt
      )
      VALUES (
        @ExpertID, @DayOfWeek, CAST(@StartTime AS TIME), CAST(@EndTime AS TIME), 
        @IsRecurring, @SpecificDate, GETDATE(), GETDATE()
      );
      
      SELECT SCOPE_IDENTITY() AS AvailabilityID;
    `);
    
    const availabilityId = result.recordset[0].AvailabilityID;
    logger.info(`New availability inserted with ID: ${availabilityId}`);
    
    return availabilityId;
  } catch (error) {
    logger.error('Error adding expert availability:', error);
    throw error;
  }
};

/**
 * Get availability by ID
 * @param {number} availabilityId - Availability ID
 * @returns {Promise<object|null>} - Availability data or null if not found
 */
const getAvailabilityById = async (availabilityId) => {
  try {
    const request = pool.request();
    request.input('AvailabilityID', sql.Int, availabilityId);
    
    const result = await request.query(`
      SELECT *
      FROM dbo.ExpertAvailability
      WHERE AvailabilityID = @AvailabilityID
    `);
    
    if (result.recordset.length === 0) {
      return null;
    }
    
    return result.recordset[0];
  } catch (error) {
    logger.error(`Error getting availability by ID ${availabilityId}:`, error);
    throw error;
  }
};

/**
 * Update expert availability slot
 * @param {number} availabilityId - Availability ID
 * @param {object} availabilityData - Availability data
 * @returns {Promise<boolean>} - True if successful
 */
const updateExpertAvailability = async (availabilityId, availabilityData) => {
  try {
    // Önce kaydın var olup olmadığını kontrol et
    const checkRequest = pool.request();
    checkRequest.input('AvailabilityID', sql.Int, availabilityId);
    
    const checkResult = await checkRequest.query(`
      SELECT AvailabilityID, ExpertID
      FROM dbo.ExpertAvailability
      WHERE AvailabilityID = @AvailabilityID
    `);
    
    if (checkResult.recordset.length === 0) {
      logger.error(`Availability record not found for ID: ${availabilityId}`);
      return false;
    }
    
    // Kaydın bağlı olduğu uzman bilgisini logla
    const expertId = checkResult.recordset[0].ExpertID;
    logger.info(`Found availability record with ID: ${availabilityId} for ExpertID: ${expertId}`);
    
    const request = pool.request();
    request.input('AvailabilityID', sql.Int, availabilityId);
    request.input('DayOfWeek', sql.Int, availabilityData.dayOfWeek);
    
    // SQL Server TIME tipini doğru şekilde işleme
    // Bu konuda emin olmak için, zaman değerlerinin tam olarak HH:MM:SS formatında olduğundan emin olalım
    const formatTimeString = (timeString) => {
      if (!timeString) {
        logger.warn('Null or undefined time string received');
        return null;
      }
      
      // Eğer HH:MM formatındaysa, saniye ekle
      if (/^\d{2}:\d{2}$/.test(timeString)) {
        logger.info(`Formatting time from '${timeString}' to '${timeString}:00'`);
        return `${timeString}:00`;
      }
      
      // Eğer zaten doğru formattaysa veya başka bir formattaysa kontrol et
      if (/^\d{2}:\d{2}:\d{2}$/.test(timeString)) {
        logger.info(`Time string already in correct format: '${timeString}'`);
      } else {
        logger.warn(`Unexpected time format: '${timeString}', attempting to use as is`);
      }
      
      return timeString;
    };
    
    const formattedStartTime = formatTimeString(availabilityData.startTime);
    const formattedEndTime = formatTimeString(availabilityData.endTime);
    
    logger.info('Formatted time values for update:', {
      original: {
        startTime: availabilityData.startTime,
        endTime: availabilityData.endTime
      },
      formatted: {
        startTime: formattedStartTime,
        endTime: formattedEndTime
      }
    });
    
    // TIME değerlerini VarChar olarak gönder ve sonra SQL tarafında dönüştür
    request.input('StartTime', sql.VarChar(8), formattedStartTime);
    request.input('EndTime', sql.VarChar(8), formattedEndTime);
    
    const isRecurring = availabilityData.isRecurring !== undefined ? availabilityData.isRecurring : true;
    request.input('IsRecurring', sql.Bit, isRecurring);
    
    // SpecificDate null veya boş string olabilir, bunu kontrol et
    const specificDate = availabilityData.specificDate === "" ? null : availabilityData.specificDate;
    request.input('SpecificDate', sql.Date, specificDate);
    
    logger.info('Final values for SQL update query:', {
      availabilityId,
      dayOfWeek: availabilityData.dayOfWeek,
      startTime: formattedStartTime,
      endTime: formattedEndTime,
      isRecurring: isRecurring,
      specificDate: specificDate
    });
    
    const result = await request.query(`
      UPDATE dbo.ExpertAvailability
      SET 
        DayOfWeek = @DayOfWeek,
        StartTime = CAST(@StartTime AS TIME),
        EndTime = CAST(@EndTime AS TIME),
        IsRecurring = @IsRecurring,
        SpecificDate = @SpecificDate,
        UpdatedAt = GETDATE()
      WHERE 
        AvailabilityID = @AvailabilityID;
      
      SELECT @@ROWCOUNT AS AffectedRows;
    `);
    
    const affectedRows = result.recordset[0].AffectedRows;
    logger.info(`Update affected ${affectedRows} rows for availability ID: ${availabilityId}`);
    
    return affectedRows > 0;
  } catch (error) {
    logger.error('Error updating expert availability:', error);
    throw error;
  }
};

/**
 * Delete expert availability slot
 * @param {number} availabilityId - Availability ID
 * @returns {Promise<boolean>} - True if successful
 */
const deleteExpertAvailability = async (availabilityId) => {
  try {
    const request = pool.request();
    request.input('AvailabilityID', sql.Int, availabilityId);
    
    const result = await request.query(`
      DELETE FROM dbo.ExpertAvailability
      WHERE AvailabilityID = @AvailabilityID;
      
      SELECT @@ROWCOUNT AS AffectedRows;
    `);
    
    return result.recordset[0].AffectedRows > 0;
  } catch (error) {
    logger.error('Error deleting expert availability:', error);
    throw error;
  }
};

/**
 * Get expert availability by date range
 * @param {number} expertId - Expert ID
 * @param {string} startDate - Start date (ISO format)
 * @param {string} endDate - End date (ISO format)
 * @returns {Promise<Array>} - Availability slots
 */
const getExpertAvailabilityByDateRange = async (expertId, startDate, endDate) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);
    request.input('StartDate', sql.Date, startDate);
    request.input('EndDate', sql.Date, endDate);
    
    const result = await request.query(`
      SELECT
        a.AvailabilityID,
        a.ExpertID,
        a.DayOfWeek,
        a.StartTime,
        a.EndTime,
        a.IsRecurring,
        a.SpecificDate,
        a.CreatedAt,
        a.UpdatedAt
      FROM
        dbo.ExpertAvailability a
      WHERE
        a.ExpertID = @ExpertID
        AND (
          -- Specific dates in the range
          (a.SpecificDate IS NOT NULL AND a.SpecificDate BETWEEN @StartDate AND @EndDate)
          OR
          -- Recurring slots
          (a.IsRecurring = 1 AND a.SpecificDate IS NULL)
        )
      ORDER BY
        CASE WHEN a.SpecificDate IS NULL THEN 1 ELSE 0 END,
        a.SpecificDate,
        a.DayOfWeek,
        a.StartTime
    `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error getting expert availability by date range:', error);
    throw error;
  }
};

/**
 * Get all expert categories
 * @returns {Promise<Array>} - List of categories
 */
const getAllCategories = async () => {
  try {
    const result = await pool.request().query(`
      SELECT 
        CategoryID as id,
        CategoryName as name,
        Description as description,
        ParentCategoryID as parentCategoryId,
        IsActive as isActive
      FROM 
        dbo.ExpertCategories
      WHERE 
        IsActive = 1
      ORDER BY 
        CategoryName
    `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error getting all categories:', error);
    throw error;
  }
};

/**
 * Get all languages
 * @returns {Promise<Array>} - List of languages
 */
const getAllLanguages = async () => {
  try {
    const result = await pool.request().query(`
      SELECT 
        LanguageID as id,
        LanguageName as name,
        LanguageCode as code,
        IsActive as isActive
      FROM 
        dbo.Languages
      WHERE 
        IsActive = 1
      ORDER BY 
        LanguageName
    `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error getting all languages:', error);
    throw error;
  }
};

/**
 * Get all specializations
 * @returns {Promise<Array>} - List of specializations
 */
const getAllSpecializations = async () => {
  try {
    const result = await pool.request().query(`
      SELECT 
        SpecializationID as id,
        SpecializationName as name,
        Description as description,
        IsActive as isActive
      FROM 
        dbo.Specializations
      WHERE 
        IsActive = 1
      ORDER BY 
        SpecializationName
    `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error getting all specializations:', error);
    throw error;
  }
};

/**
 * Get expert categories
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} - List of expert categories
 */
const getExpertCategories = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);
    
    const result = await request.query(`
      SELECT 
        c.CategoryID as id,
        c.CategoryName as name,
        c.Description as description
      FROM 
        dbo.ExpertCategoryMapping m
      JOIN 
        dbo.ExpertCategories c ON m.CategoryID = c.CategoryID
      WHERE 
        m.ExpertID = @ExpertID
      ORDER BY 
        c.CategoryName
    `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error getting expert categories:', error);
    throw error;
  }
};

/**
 * Update expert categories
 * @param {number} expertId - Expert ID
 * @param {Array} categoryIds - List of category IDs
 * @returns {Promise<boolean>} - True if successful
 */
const updateExpertCategories = async (expertId, categoryIds) => {
  try {
    const transaction = new sql.Transaction(pool);
    await transaction.begin();
    
    try {
      // Delete existing mappings
      const deleteRequest = new sql.Request(transaction);
      deleteRequest.input('ExpertID', sql.Int, expertId);
      await deleteRequest.query(`
        DELETE FROM dbo.ExpertCategoryMapping
        WHERE ExpertID = @ExpertID
      `);
      
      // Insert new mappings
      if (categoryIds && categoryIds.length > 0) {
        for (const categoryId of categoryIds) {
          const insertRequest = new sql.Request(transaction);
          insertRequest.input('ExpertID', sql.Int, expertId);
          insertRequest.input('CategoryID', sql.Int, categoryId);
          await insertRequest.query(`
            INSERT INTO dbo.ExpertCategoryMapping (ExpertID, CategoryID, CreatedAt)
            VALUES (@ExpertID, @CategoryID, GETDATE())
          `);
        }
      }
      
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      logger.error('Error updating expert categories (transaction):', error);
      throw error;
    }
  } catch (error) {
    logger.error('Error updating expert categories:', error);
    throw error;
  }
};

/**
 * Get expert languages
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} - List of expert languages
 */
const getExpertLanguages = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);
    
    const result = await request.query(`
      SELECT 
        l.LanguageID as id,
        l.LanguageName as name,
        l.LanguageCode as code,
        el.ProficiencyLevel as proficiencyLevel
      FROM 
        dbo.ExpertLanguages el
      JOIN 
        dbo.Languages l ON el.LanguageID = l.LanguageID
      WHERE 
        el.ExpertID = @ExpertID
      ORDER BY 
        l.LanguageName
    `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error getting expert languages:', error);
    throw error;
  }
};

/**
 * Update expert languages
 * @param {number} expertId - Expert ID
 * @param {Array} languages - List of language objects with id and proficiencyLevel
 * @returns {Promise<boolean>} - True if successful
 */
const updateExpertLanguages = async (expertId, languages) => {
  try {
    const transaction = new sql.Transaction(pool);
    await transaction.begin();
    
    try {
      // Delete existing mappings
      const deleteRequest = new sql.Request(transaction);
      deleteRequest.input('ExpertID', sql.Int, expertId);
      await deleteRequest.query(`
        DELETE FROM dbo.ExpertLanguages
        WHERE ExpertID = @ExpertID
      `);
      
      // Insert new mappings
      if (languages && languages.length > 0) {
        for (const language of languages) {
          const insertRequest = new sql.Request(transaction);
          insertRequest.input('ExpertID', sql.Int, expertId);
          insertRequest.input('LanguageID', sql.Int, language.id);
          insertRequest.input('ProficiencyLevel', sql.NVarChar(20), language.proficiencyLevel || 'Orta');
          await insertRequest.query(`
            INSERT INTO dbo.ExpertLanguages (ExpertID, LanguageID, ProficiencyLevel)
            VALUES (@ExpertID, @LanguageID, @ProficiencyLevel)
          `);
        }
      }
      
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      logger.error('Error updating expert languages (transaction):', error);
      throw error;
    }
  } catch (error) {
    logger.error('Error updating expert languages:', error);
    throw error;
  }
};

/**
 * Get expert specializations
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} - List of expert specializations
 */
const getExpertSpecializations = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);
    
    const result = await request.query(`
      SELECT 
        s.SpecializationID as id,
        s.SpecializationName as name,
        s.Description as description
      FROM 
        dbo.ExpertSpecializations es
      JOIN 
        dbo.Specializations s ON es.SpecializationID = s.SpecializationID
      WHERE 
        es.ExpertID = @ExpertID
      ORDER BY 
        s.SpecializationName
    `);
    
    return result.recordset;
  } catch (error) {
    logger.error('Error getting expert specializations:', error);
    throw error;
  }
};

/**
 * Update expert specializations
 * @param {number} expertId - Expert ID
 * @param {Array} specializationIds - List of specialization IDs
 * @returns {Promise<boolean>} - True if successful
 */
const updateExpertSpecializations = async (expertId, specializationIds) => {
  try {
    const transaction = new sql.Transaction(pool);
    await transaction.begin();
    
    try {
      // Delete existing mappings
      const deleteRequest = new sql.Request(transaction);
      deleteRequest.input('ExpertID', sql.Int, expertId);
      await deleteRequest.query(`
        DELETE FROM dbo.ExpertSpecializations
        WHERE ExpertID = @ExpertID
      `);
      
      // Insert new mappings
      if (specializationIds && specializationIds.length > 0) {
        for (const specializationId of specializationIds) {
          const insertRequest = new sql.Request(transaction);
          insertRequest.input('ExpertID', sql.Int, expertId);
          insertRequest.input('SpecializationID', sql.Int, specializationId);
          await insertRequest.query(`
            INSERT INTO dbo.ExpertSpecializations (ExpertID, SpecializationID)
            VALUES (@ExpertID, @SpecializationID)
          `);
        }
      }
      
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      logger.error('Error updating expert specializations (transaction):', error);
      throw error;
    }
  } catch (error) {
    logger.error('Error updating expert specializations:', error);
    throw error;
  }
};

/**
 * Get all specialties
 * @returns {Promise<Array>} - Array of specialties
 */
const getAllSpecialties = async () => {
  try {
    const request = pool.request();
    const result = await request.query(`
      SELECT
        SpecialtyID as id,
        Name as name,
        Description as description
      FROM dbo.Specialties
      WHERE IsActive = 1
      ORDER BY Name
    `);

    return result.recordset;
  } catch (error) {
    logger.error('Error getting all specialties:', error);
    throw error;
  }
};

/**
 * Get expert specialties
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} - Array of expert specialties
 */
const getExpertSpecialties = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);

    const result = await request.query(`
      SELECT
        s.SpecialtyID as id,
        s.Name as name,
        s.Description as description
      FROM dbo.ExpertSpecialties es
      JOIN dbo.Specialties s ON es.SpecialtyID = s.SpecialtyID
      WHERE es.ExpertID = @ExpertID AND s.IsActive = 1
      ORDER BY s.Name
    `);

    return result.recordset;
  } catch (error) {
    logger.error('Error getting expert specialties:', error);
    throw error;
  }
};

/**
 * Update expert specialties
 * @param {number} expertId - Expert ID
 * @param {Array} specialtyIds - Array of specialty IDs
 * @returns {Promise<boolean>} - True if successful
 */
const updateExpertSpecialties = async (expertId, specialtyIds) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);

    // Önce mevcut uzmanlık alanlarını sil
    await request.query(`
      DELETE FROM dbo.ExpertSpecialties
      WHERE ExpertID = @ExpertID
    `);

    // Yeni uzmanlık alanlarını ekle
    if (specialtyIds && specialtyIds.length > 0) {
      for (const specialtyId of specialtyIds) {
        const insertRequest = pool.request();
        insertRequest.input('ExpertID', sql.Int, expertId);
        insertRequest.input('SpecialtyID', sql.Int, specialtyId);

        await insertRequest.query(`
          INSERT INTO dbo.ExpertSpecialties (ExpertID, SpecialtyID)
          VALUES (@ExpertID, @SpecialtyID)
        `);
      }
    }

    return true;
  } catch (error) {
    logger.error('Error updating expert specialties:', error);
    throw error;
  }
};

/**
 * Get expert appointments
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} - List of appointments
 */
const getExpertAppointments = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);

    const result = await request.query(`
      SELECT
        a.AppointmentID,
        a.ClientID,
        a.ExpertID,
        a.AppointmentDate,
        a.EndTime,
        a.Status,
        a.Title,
        a.Notes,
        a.CancellationReason,
        a.IsPaid,
        a.PaymentAmount,
        a.MeetingLink,
        a.GoogleEventId,
        a.CreatedAt,
        a.UpdatedAt,
        u.FirstName as ClientFirstName,
        u.LastName as ClientLastName,
        u.Email as ClientEmail,
        u.PhoneNumber as ClientPhone
      FROM
        dbo.Appointments a
      JOIN
        dbo.Clients c ON a.ClientID = c.ClientID
      JOIN
        dbo.Users u ON c.UserID = u.UserID
      WHERE
        a.ExpertID = @ExpertID
      ORDER BY
        a.AppointmentDate DESC
    `);

    return result.recordset;
  } catch (error) {
    logger.error('Error getting expert appointments:', error);
    throw error;
  }
};

/**
 * Update appointment status
 * @param {number} appointmentId - Appointment ID
 * @param {number} expertId - Expert ID
 * @param {string} status - New status (Confirmed/Rejected)
 * @param {string} rejectionReason - Reason for rejection (optional)
 * @returns {Promise<boolean>} - Success status
 */
const updateAppointmentStatus = async (appointmentId, expertId, status, rejectionReason = null) => {
  try {
    const request = pool.request();
    request.input('AppointmentID', sql.Int, appointmentId);
    request.input('ExpertID', sql.Int, expertId);
    request.input('Status', sql.VarChar(20), status);
    request.input('UpdatedAt', sql.DateTime, new Date());

    let query = `
      UPDATE dbo.Appointments
      SET
        Status = @Status,
        UpdatedAt = @UpdatedAt
    `;

    if (rejectionReason) {
      request.input('CancellationReason', sql.NVarChar(sql.MAX), rejectionReason);
      query += `, CancellationReason = @CancellationReason`;
    }

    query += `
      WHERE
        AppointmentID = @AppointmentID
        AND ExpertID = @ExpertID
        AND Status = 'Pending'
    `;

    const result = await request.query(query);

    return result.rowsAffected[0] > 0;
  } catch (error) {
    logger.error('Error updating appointment status:', error);
    throw error;
  }
};

/**
 * Get appointment detail
 * @param {number} appointmentId - Appointment ID
 * @param {number} expertId - Expert ID
 * @returns {Promise<object|null>} - Appointment detail
 */
const getAppointmentDetail = async (appointmentId, expertId) => {
  try {
    const request = pool.request();
    request.input('AppointmentID', sql.Int, appointmentId);
    request.input('ExpertID', sql.Int, expertId);

    const result = await request.query(`
      SELECT
        a.AppointmentID as id,
        a.AppointmentDate as appointmentDate,
        a.EndTime as endTime,
        a.Status as status,
        a.Notes as notes,
        a.CancellationReason as rejectionReason,
        a.CreatedAt as createdAt,
        a.UpdatedAt as updatedAt,
        -- Client information
        u.FirstName + ' ' + u.LastName as clientName,
        u.Email as clientEmail,
        u.PhoneNumber as clientPhone,
        u.ProfileImage as clientAvatar,
        u.UserID as clientId
      FROM Appointments a
      INNER JOIN Clients cl ON a.ClientID = cl.ClientID
      INNER JOIN Users u ON cl.UserID = u.UserID
      WHERE a.AppointmentID = @AppointmentID AND a.ExpertID = @ExpertID
    `);

    if (result.recordset.length === 0) {
      return null;
    }

    return result.recordset[0];
  } catch (error) {
    logger.error('Error in getAppointmentDetail:', error);
    throw error;
  }
};

/**
 * Get expert sessions
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} Array of sessions
 */
const getExpertSessions = async (expertId) => {
  try {
    const result = await pool.request()
      .input('expertId', sql.Int, expertId)
      .query(`
        SELECT
          s.SessionID as id,
          s.AppointmentID as appointmentId,
          s.StartTime as startTime,
          s.EndTime as endTime,
          s.Duration as duration,
          s.Notes as notes,
          s.Summary as summary,
          s.Status as status,
          s.VideoURL as videoURL,
          s.FileAttachments as fileAttachments,
          s.CreatedAt as createdAt,
          s.UpdatedAt as updatedAt,
          a.Title as appointmentTitle,
          c.ClientID as clientId,
          cu.FirstName as clientFirstName,
          cu.LastName as clientLastName,
          cu.ProfileImage as clientProfileImage
        FROM Sessions s
        INNER JOIN Appointments a ON s.AppointmentID = a.AppointmentID
        INNER JOIN Clients c ON a.ClientID = c.ClientID
        INNER JOIN Users cu ON c.UserID = cu.UserID
        WHERE a.ExpertID = @expertId
        ORDER BY s.StartTime DESC
      `);

    // Format the sessions for frontend
    const formattedSessions = result.recordset.map(session => ({
      id: session.id,
      appointmentId: session.appointmentId,
      clientId: session.clientId,
      clientName: `${session.clientFirstName} ${session.clientLastName}`,
      clientAvatar: session.clientProfileImage || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.clientFirstName)}+${encodeURIComponent(session.clientLastName)}&background=4f46e5&color=fff&size=200&font-size=0.6`,
      date: session.startTime.toISOString().split('T')[0],
      startTime: session.startTime.toTimeString().slice(0, 5),
      endTime: session.endTime.toTimeString().slice(0, 5),
      duration: session.duration,
      status: session.status.toLowerCase(),
      type: 'video',
      notes: session.notes || '',
      summary: session.summary || '',
      recordingAvailable: !!session.videoURL,
      videoURL: session.videoURL,
      fileAttachments: session.fileAttachments,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt
    }));

    return formattedSessions;
  } catch (error) {
    logger.error('Error in getExpertSessions:', error);
    throw error;
  }
};

/**
 * Get expert available time slots excluding confirmed appointments
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} - List of available time slots
 */
const getExpertAvailableSlots = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);

    // First get all availability and appointments
    const result = await request.query(`
      -- Get all availability slots
      SELECT
        a.AvailabilityID as id,
        a.DayOfWeek as dayOfWeek,
        a.StartTime as startTime,
        a.EndTime as endTime,
        a.IsRecurring as isRecurring,
        a.SpecificDate as specificDate
      FROM dbo.ExpertAvailability a
      WHERE a.ExpertID = @ExpertID
      ORDER BY
        CASE WHEN a.SpecificDate IS NULL THEN 1 ELSE 0 END,
        a.SpecificDate,
        a.DayOfWeek,
        a.StartTime
    `);

    // Get confirmed appointments
    const appointmentsResult = await request.query(`
      SELECT
        AppointmentDate,
        EndTime,
        CASE
          WHEN DATEPART(WEEKDAY, AppointmentDate) = 1 THEN 7  -- Pazar -> 7
          ELSE DATEPART(WEEKDAY, AppointmentDate) - 1         -- Pazartesi=1, Salı=2, etc.
        END as DayOfWeek,
        CAST(AppointmentDate AS TIME) as StartTime,
        CAST(EndTime AS TIME) as EndTimeOnly
      FROM dbo.Appointments
      WHERE ExpertID = @ExpertID
        AND Status = 'Confirmed'
        AND AppointmentDate >= CAST(GETDATE() AS DATE)
    `);

    const appointments = appointmentsResult.recordset;
    const availabilitySlots = result.recordset;

    // Process each availability slot
    const processedSlots = [];

    for (const slot of availabilitySlots) {
      if (slot.isRecurring) {
        // For recurring slots, generate hourly time slots and filter conflicts
        const startHour = new Date(slot.startTime).getHours();
        const startMinute = new Date(slot.startTime).getMinutes();
        const endHour = new Date(slot.endTime).getHours();
        const endMinute = new Date(slot.endTime).getMinutes();

        // Generate 50-minute slots (standard session duration)
        for (let hour = startHour; hour < endHour || (hour === endHour && startMinute < endMinute); hour++) {
          const slotStart = new Date(1970, 0, 1, hour, 0, 0);
          const slotEnd = new Date(1970, 0, 1, hour, 50, 0);

          // Check if this time slot conflicts with any appointment
          const hasConflict = appointments.some(apt => {
            if (apt.DayOfWeek !== slot.dayOfWeek) return false;

            const aptStart = new Date(apt.StartTime);
            const aptEnd = new Date(apt.EndTimeOnly);

            // Check time overlap
            return (slotStart < aptEnd && slotEnd > aptStart);
          });

          if (!hasConflict) {
            processedSlots.push({
              id: `${slot.id}_${hour}`,
              dayOfWeek: slot.dayOfWeek,
              startTime: slotStart,
              endTime: slotEnd,
              isRecurring: true,
              specificDate: null
            });
          }
        }
      } else {
        // For specific date slots, check direct conflict
        const hasConflict = appointments.some(apt => {
          const aptDate = new Date(apt.AppointmentDate);
          const slotDate = new Date(slot.specificDate);

          // Check date match
          if (aptDate.toDateString() !== slotDate.toDateString()) return false;

          // Check time overlap
          const aptStart = new Date(apt.StartTime);
          const aptEnd = new Date(apt.EndTimeOnly);
          const slotStart = new Date(slot.startTime);
          const slotEnd = new Date(slot.endTime);

          return (slotStart < aptEnd && slotEnd > aptStart);
        });

        if (!hasConflict) {
          processedSlots.push({
            id: slot.id,
            dayOfWeek: slot.dayOfWeek,
            startTime: slot.startTime,
            endTime: slot.endTime,
            isRecurring: false,
            specificDate: slot.specificDate
          });
        }
      }
    }

    return processedSlots;
  } catch (error) {
    logger.error('Error in getExpertAvailableSlots:', error);
    throw error;
  }
};

/**
 * Update appointment with meeting link
 * @param {number} appointmentId - Appointment ID
 * @param {string} meetingLink - Google Meet link
 * @param {string} googleEventId - Google Calendar event ID
 * @returns {Promise<boolean>} - Success status
 */
const updateAppointmentMeetingLink = async (appointmentId, meetingLink, googleEventId) => {
  try {
    const request = pool.request();
    request.input('AppointmentID', sql.Int, appointmentId);
    request.input('MeetingLink', sql.NVarChar(500), meetingLink);
    request.input('GoogleEventId', sql.NVarChar(100), googleEventId);
    request.input('UpdatedAt', sql.DateTime, new Date());

    const result = await request.query(`
      UPDATE dbo.Appointments
      SET
        MeetingLink = @MeetingLink,
        GoogleEventId = @GoogleEventId,
        UpdatedAt = @UpdatedAt
      WHERE AppointmentID = @AppointmentID
    `);

    return result.rowsAffected[0] > 0;
  } catch (error) {
    logger.error('Error updating appointment meeting link:', error);
    throw error;
  }
};

/**
 * Get expert clients with their appointment and session statistics
 * @param {number} expertId - Expert ID
 * @returns {Promise<Array>} - List of clients with statistics
 */
const getExpertClients = async (expertId) => {
  try {
    const request = pool.request();
    request.input('ExpertID', sql.Int, expertId);

    const result = await request.query(`
      WITH ClientStats AS (
        SELECT
          u.UserID,
          u.FirstName,
          u.LastName,
          u.Email,
          u.PhoneNumber,
          u.ProfileImage,
          u.CreatedAt,
          -- Appointment statistics
          COUNT(DISTINCT a.AppointmentID) as TotalAppointments,
          COUNT(DISTINCT CASE WHEN s.Status = 'completed' THEN s.SessionID END) as CompletedSessions,
          MAX(CASE WHEN s.Status = 'completed' THEN s.EndTime END) as LastSessionDate,
          MIN(CASE WHEN a.Status = 'Confirmed' AND a.AppointmentDate > GETDATE() THEN a.AppointmentDate END) as NextSessionDate,
        MAX(CASE WHEN a.Status = 'Confirmed' THEN a.AppointmentDate END) as LastAppointmentDate,
          -- Status determination
          CASE
            WHEN COUNT(DISTINCT CASE WHEN a.Status = 'Confirmed' AND a.AppointmentDate > GETDATE() THEN a.AppointmentID END) > 0 THEN 'active'
            WHEN COUNT(DISTINCT CASE WHEN s.Status = 'completed' THEN s.SessionID END) > 0 THEN 'inactive'
            ELSE 'new'
          END as Status,
          -- Payment status (simplified)
          CASE WHEN COUNT(DISTINCT CASE WHEN a.IsPaid = 1 THEN a.AppointmentID END) > 0 THEN 'paid' ELSE 'pending' END as PaymentStatus
        FROM Users u
        INNER JOIN Appointments a ON u.UserID = a.ClientID
        LEFT JOIN Sessions s ON a.AppointmentID = s.AppointmentID
        WHERE a.ExpertID = @ExpertID
        GROUP BY u.UserID, u.FirstName, u.LastName, u.Email, u.PhoneNumber, u.ProfileImage, u.CreatedAt
      )
      SELECT
        UserID as id,
        FirstName,
        LastName,
        Email,
        PhoneNumber as phone,
        ProfileImage as avatar,
        CreatedAt,
        Status as status,
        TotalAppointments as totalSessions,
        CompletedSessions as completedSessions,
        COALESCE(LastSessionDate, LastAppointmentDate) as lastSession,
        NextSessionDate as nextSession,
        PaymentStatus as paymentStatus,
        'Standart Paket' as package, -- Default package, can be enhanced later
        'Genel' as tags -- Default tags, can be enhanced later
      FROM ClientStats
      ORDER BY
        CASE
          WHEN Status = 'new' THEN 1
          WHEN Status = 'active' THEN 2
          ELSE 3
        END,
        CreatedAt DESC
    `);

    return result.recordset;
  } catch (error) {
    logger.error('Error in getExpertClients:', error);
    throw error;
  }
};

module.exports = {
  getAllExperts,
  getExpertById,
  getExpertByUserId,
  updateExpertProfile,
  getExpertAvailability,
  addExpertAvailability,
  getAvailabilityById,
  updateExpertAvailability,
  deleteExpertAvailability,
  getExpertAvailabilityByDateRange,
  getAllCategories,
  getAllLanguages,
  getAllSpecializations,
  getAllSpecialties,
  getExpertSpecialties,
  updateExpertSpecialties,
  getExpertCategories,
  updateExpertCategories,
  getExpertLanguages,
  updateExpertLanguages,
  getExpertSpecializations,
  updateExpertSpecializations,
  getExpertAppointments,
  getAppointmentDetail,
  updateAppointmentStatus,
  updateAppointmentMeetingLink,
  getExpertSessions,
  getExpertAvailableSlots,
  getExpertClients
};