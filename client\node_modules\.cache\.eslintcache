[{"C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\index.jsx": "1", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\App.jsx": "2", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\contexts\\AuthContext.jsx": "3", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\hooks\\useAuth.js": "4", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PagePermission.jsx": "5", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ProtectedRoute.jsx": "6", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PageTransition.jsx": "7", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\MainLayout.jsx": "8", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\AuthLayout.jsx": "9", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\services\\api.js": "10", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\LoginPage.jsx": "11", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\RegisterPage.jsx": "12", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx": "13", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\dashboard\\DashboardPage.jsx": "14", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\profile\\ProfilePage.jsx": "15", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\dashboard\\ExpertDashboardPage.jsx": "16", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\profile\\ExpertProfilePage.jsx": "17", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UsersPage.jsx": "18", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\profile\\ClientProfilePage.jsx": "19", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UserFormPage.jsx": "20", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolesPage.jsx": "21", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RoleFormPage.jsx": "22", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\permissions\\PermissionsPage.jsx": "23", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolePermissionsPage.jsx": "24", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\audit-logs\\AuditLogsPage.jsx": "25", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\settings\\SystemSettingsPage.jsx": "26", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\profile\\AdminProfilePage.jsx": "27", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\index.js": "28", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\index.js": "29", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\index.js": "30", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\index.js": "31", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\index.js": "32", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\index.js": "33", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\index.js": "34", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\index.js": "35", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\index.js": "36", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\index.js": "37", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\index.js": "38", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\index.js": "39", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\index.js": "40", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\AvailabilityPage.jsx": "41", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\SessionsPage.jsx": "42", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\AppointmentsPage.jsx": "43", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\MessagesPage.jsx": "44", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\ReportsPage.jsx": "45", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\ClientsPage.jsx": "46", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\components\\PermissionMatrix.jsx": "47", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\ClientAppointmentsPage.jsx": "48", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\ClientDashboardPage.jsx": "49", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertsPage.jsx": "50", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\ClientMessagesPage.jsx": "51", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\index.js": "52", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\ClientPackagesPage.jsx": "53", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\ClientSessionsPage.jsx": "54", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\ClientPaymentsPage.jsx": "55", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormTextarea.jsx": "56", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormSelect.jsx": "57", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormRadio.jsx": "58", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormCheckbox.jsx": "59", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormInput.jsx": "60", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\MultiSelect.jsx": "61", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertDetailPage.jsx": "62", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\BookAppointmentPage.jsx": "63", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\notifications\\NotificationCenter.jsx": "64"}, {"size": 1078, "mtime": 1742731191824, "results": "65", "hashOfConfig": "66"}, {"size": 12732, "mtime": 1754673777859, "results": "67", "hashOfConfig": "66"}, {"size": 12010, "mtime": 1743023176067, "results": "68", "hashOfConfig": "66"}, {"size": 283, "mtime": 1742651186300, "results": "69", "hashOfConfig": "66"}, {"size": 1200, "mtime": 1742844616558, "results": "70", "hashOfConfig": "66"}, {"size": 808, "mtime": 1742651192925, "results": "71", "hashOfConfig": "66"}, {"size": 813, "mtime": 1743121345478, "results": "72", "hashOfConfig": "66"}, {"size": 26360, "mtime": 1754914178556, "results": "73", "hashOfConfig": "66"}, {"size": 840, "mtime": 1742651199783, "results": "74", "hashOfConfig": "66"}, {"size": 4044, "mtime": 1754906950669, "results": "75", "hashOfConfig": "66"}, {"size": 5161, "mtime": 1743023176067, "results": "76", "hashOfConfig": "66"}, {"size": 13594, "mtime": 1743023175878, "results": "77", "hashOfConfig": "66"}, {"size": 3680, "mtime": 1742839996729, "results": "78", "hashOfConfig": "66"}, {"size": 6149, "mtime": 1742652514170, "results": "79", "hashOfConfig": "66"}, {"size": 10004, "mtime": 1742687855831, "results": "80", "hashOfConfig": "66"}, {"size": 25808, "mtime": 1754942504054, "results": "81", "hashOfConfig": "66"}, {"size": 34266, "mtime": 1754517675551, "results": "82", "hashOfConfig": "66"}, {"size": 7579, "mtime": 1742681038274, "results": "83", "hashOfConfig": "66"}, {"size": 18240, "mtime": 1743121346919, "results": "84", "hashOfConfig": "66"}, {"size": 13581, "mtime": 1742729246617, "results": "85", "hashOfConfig": "66"}, {"size": 9374, "mtime": 1742685959799, "results": "86", "hashOfConfig": "66"}, {"size": 21513, "mtime": 1743085705486, "results": "87", "hashOfConfig": "66"}, {"size": 5307, "mtime": 1742686008746, "results": "88", "hashOfConfig": "66"}, {"size": 915, "mtime": 1742668681019, "results": "89", "hashOfConfig": "66"}, {"size": 10152, "mtime": 1742686053807, "results": "90", "hashOfConfig": "66"}, {"size": 16081, "mtime": 1742653511611, "results": "91", "hashOfConfig": "66"}, {"size": 183, "mtime": 1754672350696, "results": "92", "hashOfConfig": "66"}, {"size": 87, "mtime": 1742911290814, "results": "93", "hashOfConfig": "66"}, {"size": 75, "mtime": 1742926306611, "results": "94", "hashOfConfig": "66"}, {"size": 87, "mtime": 1742924113827, "results": "95", "hashOfConfig": "66"}, {"size": 75, "mtime": 1742928519256, "results": "96", "hashOfConfig": "66"}, {"size": 72, "mtime": 1742930957065, "results": "97", "hashOfConfig": "66"}, {"size": 72, "mtime": 1742930563398, "results": "98", "hashOfConfig": "66"}, {"size": 96, "mtime": 1742942293117, "results": "99", "hashOfConfig": "66"}, {"size": 189, "mtime": 1754672075729, "results": "100", "hashOfConfig": "66"}, {"size": 195, "mtime": 1754673752486, "results": "101", "hashOfConfig": "66"}, {"size": 93, "mtime": 1742952326351, "results": "102", "hashOfConfig": "66"}, {"size": 93, "mtime": 1742951288236, "results": "103", "hashOfConfig": "66"}, {"size": 93, "mtime": 1743107128763, "results": "104", "hashOfConfig": "66"}, {"size": 91, "mtime": 1743107140295, "results": "105", "hashOfConfig": "66"}, {"size": 33144, "mtime": 1754676655256, "results": "106", "hashOfConfig": "66"}, {"size": 22569, "mtime": 1754946402777, "results": "107", "hashOfConfig": "66"}, {"size": 22820, "mtime": 1754942357182, "results": "108", "hashOfConfig": "66"}, {"size": 35391, "mtime": 1754858963157, "results": "109", "hashOfConfig": "66"}, {"size": 33648, "mtime": 1742995680878, "results": "110", "hashOfConfig": "66"}, {"size": 19462, "mtime": 1754920395097, "results": "111", "hashOfConfig": "66"}, {"size": 8841, "mtime": 1742669558119, "results": "112", "hashOfConfig": "66"}, {"size": 21857, "mtime": 1754942357181, "results": "113", "hashOfConfig": "66"}, {"size": 25889, "mtime": 1742943667842, "results": "114", "hashOfConfig": "66"}, {"size": 29956, "mtime": 1754672945896, "results": "115", "hashOfConfig": "66"}, {"size": 37983, "mtime": 1754858963346, "results": "116", "hashOfConfig": "66"}, {"size": 295, "mtime": 1743101819804, "results": "117", "hashOfConfig": "66"}, {"size": 23208, "mtime": 1742991622871, "results": "118", "hashOfConfig": "66"}, {"size": 24976, "mtime": 1754993620449, "results": "119", "hashOfConfig": "66"}, {"size": 40654, "mtime": 1742991622874, "results": "120", "hashOfConfig": "66"}, {"size": 3969, "mtime": 1742671375144, "results": "121", "hashOfConfig": "66"}, {"size": 4340, "mtime": 1742671396113, "results": "122", "hashOfConfig": "66"}, {"size": 2654, "mtime": 1742671423203, "results": "123", "hashOfConfig": "66"}, {"size": 2614, "mtime": 1742671410454, "results": "124", "hashOfConfig": "66"}, {"size": 4148, "mtime": 1742671355716, "results": "125", "hashOfConfig": "66"}, {"size": 4754, "mtime": 1754517017782, "results": "126", "hashOfConfig": "66"}, {"size": 18012, "mtime": 1754673063946, "results": "127", "hashOfConfig": "66"}, {"size": 16547, "mtime": 1754942357358, "results": "128", "hashOfConfig": "66"}, {"size": 13774, "mtime": 1754916816984, "results": "129", "hashOfConfig": "66"}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t9pu2d", {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\index.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\App.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\contexts\\AuthContext.jsx", ["322"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\hooks\\useAuth.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PagePermission.jsx", ["323"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PageTransition.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\MainLayout.jsx", ["324"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\AuthLayout.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\services\\api.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\LoginPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\RegisterPage.jsx", ["325"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\dashboard\\DashboardPage.jsx", ["326", "327", "328"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\profile\\ProfilePage.jsx", ["329"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\dashboard\\ExpertDashboardPage.jsx", ["330", "331", "332", "333", "334"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\profile\\ExpertProfilePage.jsx", ["335"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UsersPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\profile\\ClientProfilePage.jsx", ["336", "337"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UserFormPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolesPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RoleFormPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\permissions\\PermissionsPage.jsx", ["338", "339"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolePermissionsPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\audit-logs\\AuditLogsPage.jsx", ["340"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\settings\\SystemSettingsPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\profile\\AdminProfilePage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\AvailabilityPage.jsx", ["341", "342", "343", "344", "345", "346"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\SessionsPage.jsx", ["347", "348", "349", "350", "351", "352", "353"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\AppointmentsPage.jsx", ["354", "355", "356", "357", "358", "359", "360"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\MessagesPage.jsx", ["361", "362", "363", "364", "365", "366", "367", "368", "369", "370"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\ReportsPage.jsx", ["371", "372", "373"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\ClientsPage.jsx", ["374", "375", "376", "377", "378"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\components\\PermissionMatrix.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\ClientAppointmentsPage.jsx", ["379", "380", "381", "382", "383", "384", "385", "386", "387", "388"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\ClientDashboardPage.jsx", ["389", "390", "391", "392"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertsPage.jsx", ["393", "394", "395", "396", "397"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\ClientMessagesPage.jsx", ["398", "399", "400", "401", "402", "403", "404", "405", "406"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\ClientPackagesPage.jsx", ["407", "408"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\ClientSessionsPage.jsx", ["409", "410", "411", "412", "413", "414", "415", "416", "417", "418"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\ClientPaymentsPage.jsx", ["419", "420", "421"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormTextarea.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormSelect.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormRadio.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormCheckbox.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormInput.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\MultiSelect.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertDetailPage.jsx", ["422", "423", "424", "425", "426"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\BookAppointmentPage.jsx", ["427", "428"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\notifications\\NotificationCenter.jsx", ["429", "430", "431"], [], {"ruleId": "432", "severity": 1, "message": "433", "line": 75, "column": 6, "nodeType": "434", "endLine": 75, "endColumn": 8, "suggestions": "435"}, {"ruleId": "436", "severity": 1, "message": "437", "line": 10, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 10, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "440", "line": 1, "column": 27, "nodeType": "438", "messageId": "439", "endLine": 1, "endColumn": 36}, {"ruleId": "436", "severity": 1, "message": "441", "line": 11, "column": 21, "nodeType": "438", "messageId": "439", "endLine": 11, "endColumn": 33}, {"ruleId": "442", "severity": 1, "message": "443", "line": 30, "column": 15, "nodeType": "444", "endLine": 30, "endColumn": 91}, {"ruleId": "442", "severity": 1, "message": "443", "line": 57, "column": 15, "nodeType": "444", "endLine": 57, "endColumn": 91}, {"ruleId": "442", "severity": 1, "message": "443", "line": 84, "column": 15, "nodeType": "444", "endLine": 84, "endColumn": 91}, {"ruleId": "436", "severity": 1, "message": "445", "line": 6, "column": 21, "nodeType": "438", "messageId": "439", "endLine": 6, "endColumn": 33}, {"ruleId": "436", "severity": 1, "message": "446", "line": 12, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 12, "endColumn": 19}, {"ruleId": "436", "severity": 1, "message": "447", "line": 14, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 14, "endColumn": 16}, {"ruleId": "436", "severity": 1, "message": "448", "line": 29, "column": 34, "nodeType": "438", "messageId": "439", "endLine": 29, "endColumn": 59}, {"ruleId": "436", "severity": 1, "message": "449", "line": 130, "column": 54, "nodeType": "438", "messageId": "439", "endLine": 130, "endColumn": 61}, {"ruleId": "436", "severity": 1, "message": "450", "line": 133, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 133, "endColumn": 23}, {"ruleId": "436", "severity": 1, "message": "451", "line": 29, "column": 10, "nodeType": "438", "messageId": "439", "endLine": 29, "endColumn": 20}, {"ruleId": "436", "severity": 1, "message": "445", "line": 6, "column": 47, "nodeType": "438", "messageId": "439", "endLine": 6, "endColumn": 59}, {"ruleId": "436", "severity": 1, "message": "452", "line": 24, "column": 10, "nodeType": "438", "messageId": "439", "endLine": 24, "endColumn": 20}, {"ruleId": "436", "severity": 1, "message": "453", "line": 9, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 9, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "454", "line": 12, "column": 10, "nodeType": "438", "messageId": "439", "endLine": 12, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "453", "line": 9, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 9, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "455", "line": 16, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 16, "endColumn": 20}, {"ruleId": "436", "severity": 1, "message": "456", "line": 19, "column": 51, "nodeType": "438", "messageId": "439", "endLine": 19, "endColumn": 58}, {"ruleId": "436", "severity": 1, "message": "437", "line": 28, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 28, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "457", "line": 42, "column": 10, "nodeType": "438", "messageId": "439", "endLine": 42, "endColumn": 20}, {"ruleId": "432", "severity": 1, "message": "458", "line": 54, "column": 6, "nodeType": "434", "endLine": 54, "endColumn": 8, "suggestions": "459"}, {"ruleId": "436", "severity": 1, "message": "460", "line": 397, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 397, "endColumn": 22}, {"ruleId": "436", "severity": 1, "message": "461", "line": 9, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 9, "endColumn": 14}, {"ruleId": "436", "severity": 1, "message": "462", "line": 11, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 11, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "463", "line": 15, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 15, "endColumn": 11}, {"ruleId": "436", "severity": 1, "message": "464", "line": 17, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 17, "endColumn": 16}, {"ruleId": "436", "severity": 1, "message": "465", "line": 18, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 18, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "437", "line": 30, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 30, "endColumn": 15}, {"ruleId": "432", "severity": 1, "message": "466", "line": 48, "column": 6, "nodeType": "434", "endLine": 48, "endColumn": 8, "suggestions": "467"}, {"ruleId": "436", "severity": 1, "message": "468", "line": 4, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 4, "endColumn": 14}, {"ruleId": "436", "severity": 1, "message": "469", "line": 7, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 7, "endColumn": 11}, {"ruleId": "436", "severity": 1, "message": "462", "line": 11, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 11, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "470", "line": 15, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 15, "endColumn": 17}, {"ruleId": "436", "severity": 1, "message": "464", "line": 16, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 16, "endColumn": 16}, {"ruleId": "436", "severity": 1, "message": "465", "line": 17, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 17, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "437", "line": 29, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 29, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "469", "line": 9, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 9, "endColumn": 11}, {"ruleId": "436", "severity": 1, "message": "471", "line": 13, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 13, "endColumn": 17}, {"ruleId": "436", "severity": 1, "message": "472", "line": 18, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 18, "endColumn": 12}, {"ruleId": "436", "severity": 1, "message": "473", "line": 20, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 20, "endColumn": 12}, {"ruleId": "436", "severity": 1, "message": "474", "line": 21, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 21, "endColumn": 12}, {"ruleId": "436", "severity": 1, "message": "475", "line": 27, "column": 10, "nodeType": "438", "messageId": "439", "endLine": 27, "endColumn": 14}, {"ruleId": "436", "severity": 1, "message": "476", "line": 43, "column": 10, "nodeType": "438", "messageId": "439", "endLine": 43, "endColumn": 21}, {"ruleId": "432", "severity": 1, "message": "477", "line": 276, "column": 6, "nodeType": "434", "endLine": 276, "endColumn": 23, "suggestions": "478"}, {"ruleId": "432", "severity": 1, "message": "479", "line": 281, "column": 6, "nodeType": "434", "endLine": 281, "endColumn": 8, "suggestions": "480"}, {"ruleId": "432", "severity": 1, "message": "477", "line": 342, "column": 6, "nodeType": "434", "endLine": 342, "endColumn": 28, "suggestions": "481"}, {"ruleId": "436", "severity": 1, "message": "469", "line": 13, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 13, "endColumn": 11}, {"ruleId": "436", "severity": 1, "message": "482", "line": 15, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 15, "endColumn": 28}, {"ruleId": "436", "severity": 1, "message": "437", "line": 256, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 256, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "455", "line": 11, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 11, "endColumn": 20}, {"ruleId": "436", "severity": 1, "message": "483", "line": 12, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 12, "endColumn": 17}, {"ruleId": "436", "severity": 1, "message": "469", "line": 13, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 13, "endColumn": 11}, {"ruleId": "436", "severity": 1, "message": "437", "line": 26, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 26, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "484", "line": 72, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 72, "endColumn": 19}, {"ruleId": "436", "severity": 1, "message": "462", "line": 11, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 11, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "485", "line": 12, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 12, "endColumn": 26}, {"ruleId": "436", "severity": 1, "message": "455", "line": 14, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 14, "endColumn": 20}, {"ruleId": "436", "severity": 1, "message": "470", "line": 15, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 15, "endColumn": 17}, {"ruleId": "436", "severity": 1, "message": "464", "line": 16, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 16, "endColumn": 16}, {"ruleId": "436", "severity": 1, "message": "465", "line": 17, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 17, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "486", "line": 19, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 19, "endColumn": 28}, {"ruleId": "436", "severity": 1, "message": "437", "line": 31, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 31, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "487", "line": 151, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 151, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "484", "line": 171, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 171, "endColumn": 19}, {"ruleId": "436", "severity": 1, "message": "462", "line": 5, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 5, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "446", "line": 12, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 12, "endColumn": 19}, {"ruleId": "436", "severity": 1, "message": "447", "line": 14, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 14, "endColumn": 16}, {"ruleId": "436", "severity": 1, "message": "448", "line": 28, "column": 34, "nodeType": "438", "messageId": "439", "endLine": 28, "endColumn": 59}, {"ruleId": "436", "severity": 1, "message": "488", "line": 6, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 6, "endColumn": 18}, {"ruleId": "436", "severity": 1, "message": "489", "line": 10, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 10, "endColumn": 29}, {"ruleId": "436", "severity": 1, "message": "490", "line": 12, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 12, "endColumn": 18}, {"ruleId": "436", "severity": 1, "message": "491", "line": 13, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 13, "endColumn": 16}, {"ruleId": "436", "severity": 1, "message": "437", "line": 24, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 24, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "471", "line": 13, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 13, "endColumn": 17}, {"ruleId": "436", "severity": 1, "message": "492", "line": 15, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 15, "endColumn": 12}, {"ruleId": "436", "severity": 1, "message": "493", "line": 17, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 17, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "473", "line": 20, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 20, "endColumn": 12}, {"ruleId": "436", "severity": 1, "message": "474", "line": 21, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 21, "endColumn": 12}, {"ruleId": "436", "severity": 1, "message": "476", "line": 45, "column": 10, "nodeType": "438", "messageId": "439", "endLine": 45, "endColumn": 21}, {"ruleId": "432", "severity": 1, "message": "477", "line": 280, "column": 6, "nodeType": "434", "endLine": 280, "endColumn": 23, "suggestions": "494"}, {"ruleId": "432", "severity": 1, "message": "479", "line": 285, "column": 6, "nodeType": "434", "endLine": 285, "endColumn": 8, "suggestions": "495"}, {"ruleId": "432", "severity": 1, "message": "477", "line": 347, "column": 6, "nodeType": "434", "endLine": 347, "endColumn": 28, "suggestions": "496"}, {"ruleId": "436", "severity": 1, "message": "437", "line": 20, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 20, "endColumn": 15}, {"ruleId": "442", "severity": 1, "message": "443", "line": 487, "column": 19, "nodeType": "444", "endLine": 490, "endColumn": 20}, {"ruleId": "436", "severity": 1, "message": "461", "line": 9, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 9, "endColumn": 14}, {"ruleId": "436", "severity": 1, "message": "497", "line": 10, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 10, "endColumn": 11}, {"ruleId": "436", "severity": 1, "message": "462", "line": 11, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 11, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "455", "line": 14, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 14, "endColumn": 20}, {"ruleId": "436", "severity": 1, "message": "463", "line": 15, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 15, "endColumn": 11}, {"ruleId": "436", "severity": 1, "message": "464", "line": 17, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 17, "endColumn": 16}, {"ruleId": "436", "severity": 1, "message": "437", "line": 31, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 31, "endColumn": 15}, {"ruleId": "432", "severity": 1, "message": "466", "line": 80, "column": 6, "nodeType": "434", "endLine": 80, "endColumn": 8, "suggestions": "498"}, {"ruleId": "436", "severity": 1, "message": "499", "line": 157, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 157, "endColumn": 14}, {"ruleId": "436", "severity": 1, "message": "487", "line": 226, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 226, "endColumn": 24}, {"ruleId": "436", "severity": 1, "message": "500", "line": 7, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 7, "endColumn": 18}, {"ruleId": "436", "severity": 1, "message": "437", "line": 25, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 25, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "501", "line": 234, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 234, "endColumn": 23}, {"ruleId": "436", "severity": 1, "message": "491", "line": 11, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 11, "endColumn": 16}, {"ruleId": "436", "severity": 1, "message": "502", "line": 15, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 15, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "492", "line": 16, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 16, "endColumn": 12}, {"ruleId": "436", "severity": 1, "message": "503", "line": 17, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 17, "endColumn": 15}, {"ruleId": "436", "severity": 1, "message": "437", "line": 27, "column": 11, "nodeType": "438", "messageId": "439", "endLine": 27, "endColumn": 15}, {"ruleId": "432", "severity": 1, "message": "504", "line": 39, "column": 6, "nodeType": "434", "endLine": 39, "endColumn": 33, "suggestions": "505"}, {"ruleId": "506", "severity": 1, "message": "507", "line": 39, "column": 7, "nodeType": "438", "messageId": "508", "endLine": 39, "endColumn": 32}, {"ruleId": "436", "severity": 1, "message": "509", "line": 5, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 5, "endColumn": 12}, {"ruleId": "436", "severity": 1, "message": "469", "line": 9, "column": 3, "nodeType": "438", "messageId": "439", "endLine": 9, "endColumn": 11}, {"ruleId": "436", "severity": 1, "message": "510", "line": 13, "column": 10, "nodeType": "438", "messageId": "439", "endLine": 13, "endColumn": 16}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserPermissions'. Either include it or remove the dependency array.", "ArrayExpression", ["511"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'registerUser' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'FormCheckbox' is defined but never used.", "'DocumentTextIcon' is defined but never used.", "'ArrowDownIcon' is defined but never used.", "'setHasUnreadNotifications' is assigned a value but never used.", "'clients' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'expertData' is assigned a value but never used.", "'clientData' is assigned a value but never used.", "'hasPermission' is assigned a value but never used.", "'pages' is assigned a value but never used.", "'ArrowDownTrayIcon' is defined but never used.", "'addDays' is defined but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAvailabilities'. Either include it or remove the dependency array.", ["512"], "'formatDayName' is assigned a value but never used.", "'XCircleIcon' is defined but never used.", "'ChartBarIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'PaperClipIcon' is defined but never used.", "'DocumentArrowDownIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadSessions'. Either include it or remove the dependency array.", ["513"], "'VideoCamera' is defined but never used.", "'UserIcon' is defined but never used.", "'PlayCircleIcon' is defined but never used.", "'UserCircleIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'XMarkIcon' is defined but never used.", "'TrashIcon' is defined but never used.", "'Link' is defined but never used.", "'typingUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'markConversationAsRead'. Either include it or remove the dependency array.", ["514"], "React Hook useEffect has a missing dependency: 'loadConversations'. Either include it or remove the dependency array.", ["515"], ["516"], "'PresentationChartLineIcon' is defined but never used.", "'CheckBadgeIcon' is defined but never used.", "'formatDate' is assigned a value but never used.", "'ChatBubbleLeftRightIcon' is defined but never used.", "'AdjustmentsHorizontalIcon' is defined but never used.", "'getStatusBorder' is assigned a value but never used.", "'ChevronDownIcon' is defined but never used.", "'ChatBubbleLeftEllipsisIcon' is defined but never used.", "'AcademicCapIcon' is defined but never used.", "'UserGroupIcon' is defined but never used.", "'PhoneIcon' is defined but never used.", "'InformationCircleIcon' is defined but never used.", ["517"], ["518"], ["519"], "'BellIcon' is defined but never used.", ["520"], "'today' is assigned a value but never used.", "'CheckCircleIcon' is defined but never used.", "'formatDateTime' is assigned a value but never used.", "'GlobeAltIcon' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'expertId' and 'navigate'. Either include them or remove the dependency array.", ["521"], "no-use-before-define", "'loadExpertAndAvailability' was used before it was defined.", "usedBeforeDefined", "'CheckIcon' is defined but never used.", "'format' is defined but never used.", {"desc": "522", "fix": "523"}, {"desc": "524", "fix": "525"}, {"desc": "526", "fix": "527"}, {"desc": "528", "fix": "529"}, {"desc": "530", "fix": "531"}, {"desc": "532", "fix": "533"}, {"desc": "528", "fix": "534"}, {"desc": "530", "fix": "535"}, {"desc": "532", "fix": "536"}, {"desc": "526", "fix": "537"}, {"desc": "538", "fix": "539"}, "Update the dependencies array to be: [fetchUserPermissions]", {"range": "540", "text": "541"}, "Update the dependencies array to be: [fetchAvailabilities]", {"range": "542", "text": "543"}, "Update the dependencies array to be: [loadSessions]", {"range": "544", "text": "545"}, "Update the dependencies array to be: [markConversationAsRead, socket, user.id]", {"range": "546", "text": "547"}, "Update the dependencies array to be: [loadConversations]", {"range": "548", "text": "549"}, "Update the dependencies array to be: [markConversationAsRead, selectedConversation]", {"range": "550", "text": "551"}, {"range": "552", "text": "547"}, {"range": "553", "text": "549"}, {"range": "554", "text": "551"}, {"range": "555", "text": "545"}, "Update the dependencies array to be: [expertId, loadExpertAndAvailability, navigate]", {"range": "556", "text": "557"}, [2597, 2599], "[fetchUserPermissions]", [1761, 1763], "[fetchAvailabilities]", [1254, 1256], "[loadSessions]", [10531, 10548], "[markConversationAsRead, socket, user.id]", [10646, 10648], "[loadConversations]", [13050, 13072], "[markConversationAsRead, selectedConversation]", [10699, 10716], [10814, 10816], [13321, 13343], [2336, 2338], [1221, 1248], "[expertId, loadExpertAndAvailability, navigate]"]