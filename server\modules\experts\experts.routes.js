/**
 * Experts Routes
 * Handles routes for expert-related API endpoints
 */

const express = require('express');
const { body } = require('express-validator');
const multer = require('multer');
const path = require('path');
const router = express.Router();
const expertsController = require('./experts.controller');
const auth = require('../../middleware/auth');
const { checkPermission } = require('../../middleware/permission');
const { ROLES } = require('../../config/constants');

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/profile-images/');
  },
  filename: function (req, file, cb) {
    // Generate secure unique filename: userId_timestamp_random.extension
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const sanitizedExtension = path.extname(file.originalname).toLowerCase().replace(/[^a-z0-9.]/g, '');
    const filename = `expert_${req.user.id}_${uniqueSuffix}${sanitizedExtension}`;
    cb(null, filename);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit (güvenlik için azalttık)
  },
  fileFilter: function (req, file, cb) {
    // Güvenli dosya türleri
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp'
    ];

    // MIME type kontrolü
    if (!allowedMimeTypes.includes(file.mimetype)) {
      return cb(new Error('Sadece JPG, PNG, GIF ve WebP dosyaları yüklenebilir!'), false);
    }

    // Dosya uzantısı kontrolü (double extension attack prevention)
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const fileExtension = path.extname(file.originalname).toLowerCase();

    if (!allowedExtensions.includes(fileExtension)) {
      return cb(new Error('Geçersiz dosya uzantısı!'), false);
    }

    // Dosya adı güvenlik kontrolü
    if (file.originalname.includes('..') || file.originalname.includes('/') || file.originalname.includes('\\')) {
      return cb(new Error('Geçersiz dosya adı!'), false);
    }

    cb(null, true);
  }
});

// ========================
// STATIK ROTALAR - ÖNCE TANIMLANMALI
// ========================

// Public static routes
/**
 * @route   GET /api/experts/categories
 * @desc    Get all categories
 * @access  Public
 */
router.get('/categories', expertsController.getAllCategories);

/**
 * @route   GET /api/experts/languages
 * @desc    Get all languages
 * @access  Public
 */
router.get('/languages', expertsController.getAllLanguages);

/**
 * @route   GET /api/experts/specializations
 * @desc    Get all specializations
 * @access  Public
 */
router.get('/specializations', expertsController.getAllSpecializations);

// Expert auth required static routes
/**
 * @route   GET /api/experts/profile/me
 * @desc    Get own expert profile
 * @access  Private (Expert only)
 */
router.get('/profile/me',
  auth,
  expertsController.getExpertProfile
);

/**
 * @route   GET /api/experts/specialties
 * @desc    Get all specialties
 * @access  Public
 */
router.get('/specialties', expertsController.getAllSpecialties);

/**
 * @route   GET /api/experts/profile/specialties
 * @desc    Get expert specialties
 * @access  Private (Expert only)
 */
router.get('/profile/specialties',
  auth,
  expertsController.getExpertSpecialties
);

/**
 * @route   PUT /api/experts/profile/specialties
 * @desc    Update expert specialties
 * @access  Private (Expert only)
 */
router.put('/profile/specialties',
  auth,
  body('specialtyIds').isArray().withMessage('Uzmanlık alanları dizi formatında olmalıdır'),
  body('specialtyIds.*').isInt().withMessage('Geçersiz uzmanlık alanı ID'),
  expertsController.updateExpertSpecialties
);

/**
 * @route   POST /api/experts/profile/upload-image
 * @desc    Upload expert profile image
 * @access  Private (Expert only)
 */
router.post('/profile/upload-image',
  auth,
  upload.single('profileImage'),
  expertsController.uploadProfileImage
);

/**
 * @route   PUT /api/experts/profile
 * @desc    Update expert profile
 * @access  Private (Expert only)
 */
router.put('/profile', [
  auth,
  // Kullanıcı bilgileri
  body('firstName').optional().trim().isLength({ min: 2, max: 50 }),
  body('lastName').optional().trim().isLength({ min: 2, max: 50 }),
  body('email').optional().trim().isEmail(),
  // Expert bilgileri
  body('specialty').optional().trim().isLength({ min: 2, max: 50 }),
  body('biography').optional().trim().isLength({ max: 2000 }),
  body('education').optional().trim().isLength({ max: 1000 }),
  body('certificates').optional().trim().isLength({ max: 1000 }),
  body('shortBio').optional().trim().isLength({ max: 200 }),
  body('experienceYears').optional().custom((value) => {
    if (value === '' || value === null || value === undefined) {
      return true; // Boş değerlere izin ver
    }
    const intValue = parseInt(value);
    if (isNaN(intValue) || intValue < 0 || intValue > 100) {
      throw new Error('Deneyim yılı 0-100 arasında bir sayı olmalıdır');
    }
    return true;
  }),
  body('hourlyRate').optional().custom((value) => {
    if (value === '' || value === null || value === undefined) {
      return true; // Boş değerlere izin ver
    }
    if (isNaN(value) || parseFloat(value) < 0) {
      throw new Error('Saatlik ücret 0 veya daha büyük bir sayı olmalıdır');
    }
    return true;
  }),
  body('profileVisibility').optional().isBoolean(),
  body('responseTime').optional().custom((value) => {
    if (value === '' || value === null || value === undefined) {
      return true;
    }
    const intValue = parseInt(value);
    if (isNaN(intValue) || intValue < 1 || intValue > 168) {
      throw new Error('Yanıt süresi 1-168 saat arasında olmalıdır');
    }
    return true;
  }),
  body('sessionDuration').optional().custom((value) => {
    if (value === '' || value === null || value === undefined) {
      return true;
    }
    const intValue = parseInt(value);
    if (isNaN(intValue) || intValue < 15 || intValue > 240) {
      throw new Error('Seans süresi 15-240 dakika arasında olmalıdır');
    }
    return true;
  }),
  body('profileImage').optional().trim().custom(value => {
    // Boş değer ise doğrula
    if (!value || value.trim() === '') return true;

    // Dosya yolu kontrolü (uploads ile başlıyorsa)
    if (value.startsWith('/uploads/')) {
      return true;
    }

    // URL kontrolü yap
    const urlPattern = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/;
    if (!urlPattern.test(value)) {
      throw new Error('Geçerli bir URL veya dosya yolu giriniz');
    }
    return true;
  }),
  body('videoIntro').optional().trim().custom(value => {
    // Boş değer ise doğrula
    if (!value || value.trim() === '') return true;
    // URL kontrolü yap
    const urlPattern = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/;
    if (!urlPattern.test(value)) {
      throw new Error('Geçerli bir URL giriniz');
    }
    return true;
  }),
  body('locationAddress').optional().trim().isLength({ max: 255 }),
  body('locationCity').optional().trim().isLength({ max: 50 }),
  body('locationCountry').optional().trim().isLength({ max: 50 })
], expertsController.updateExpertProfile);

/**
 * @route   GET /api/experts/availability/me
 * @desc    Get own availability
 * @access  Private (Expert only)
 */
router.get('/availability/me',
  auth,
  checkPermission('/expert/availabilities', 'READ'),
  expertsController.getExpertAvailability
);

/**
 * @route   GET /api/experts/appointments
 * @desc    Get expert appointments
 * @access  Private (Expert only)
 */
router.get('/appointments',
  auth,
  checkPermission('/expert/appointments', 'READ'),
  expertsController.getExpertAppointments
);

/**
 * @route   GET /api/experts/appointments/:id
 * @desc    Get appointment detail
 * @access  Private (Expert only)
 */
router.get('/appointments/:id',
  auth,
  checkPermission('/expert/appointments', 'READ'),
  expertsController.getAppointmentDetail
);

/**
 * @route   PUT /api/experts/appointments/:id/status
 * @desc    Update appointment status (approve/reject)
 * @access  Private (Expert only)
 */
router.put('/appointments/:id/status', [
  auth,
  checkPermission('/expert/appointments', 'UPDATE'),
  body('status').isIn(['Confirmed', 'Rejected']).withMessage('Status must be Confirmed or Rejected'),
  body('rejectionReason').optional().trim().isLength({ max: 500 })
], expertsController.updateAppointmentStatus);

/**
 * @route   GET /api/experts/sessions
 * @desc    Get expert sessions
 * @access  Private (Expert only)
 */
router.get('/sessions',
  auth,
  checkPermission('/expert/sessions', 'READ'),
  expertsController.getExpertSessions
);

/**
 * @route   GET /api/experts/clients
 * @desc    Get expert clients
 * @access  Private (Expert only)
 */
router.get('/clients',
  auth,
  checkPermission('/expert/clients', 'READ'),
  expertsController.getExpertClients
);

/**
 * @route   POST /api/experts/availability
 * @desc    Add availability slot
 * @access  Private (Expert only)
 */
router.post('/availability', [
  auth,
  checkPermission('/expert/availabilities', 'CREATE'),
  body('dayOfWeek').optional().isInt({ min: 1, max: 7 }),
  body('startTime').notEmpty().isString(),
  body('endTime').notEmpty().isString(),
  body('isRecurring').isBoolean(),
  body('specificDate').optional().custom(value => {
    // null değerini kabul et
    if (value === null) return true;
    // ISO 8601 formatını kontrol et
    if (!/^\d{4}-\d{2}-\d{2}(T.*)?$/.test(value)) {
      throw new Error('Geçersiz tarih formatı. ISO 8601 formatında bir tarih olmalı.');
    }
    return true;
  })
], expertsController.addExpertAvailability);

/**
 * @route   POST /api/experts/availability/recurring
 * @desc    Add recurring availability (multiple days)
 * @access  Private (Expert only)
 */
router.post('/availability/recurring', [
  auth,
  checkPermission('/expert/availabilities', 'CREATE'),
  body('daysOfWeek').isArray().withMessage('Gün listesi bir dizi olmalıdır'),
  body('daysOfWeek.*').isInt({ min: 1, max: 7 }).withMessage('Geçerli gün numaraları sağlayınız (1-7)'),
  body('startTime').notEmpty().isString(),
  body('endTime').notEmpty().isString(),
], expertsController.addRecurringAvailability);

/**
 * @route   POST /api/experts/availability/specific-date
 * @desc    Add availability for a specific date
 * @access  Private (Expert only)
 */
router.post('/availability/specific-date', [
  auth,
  checkPermission('/expert/availabilities', 'CREATE'),
  body('date').notEmpty().isISO8601(),
  body('startTime').notEmpty().isString(),
  body('endTime').notEmpty().isString(),
], expertsController.addSpecificDateAvailability);

/**
 * @route   GET /api/experts/availability/range
 * @desc    Get availability by date range
 * @access  Private (Expert only)
 */
router.get('/availability/range', [
  auth,
  checkPermission('/expert/availabilities', 'READ'),
], expertsController.getAvailabilityByDateRange);

/**
 * @route   PUT /api/experts/availability/:availabilityId
 * @desc    Update availability slot
 * @access  Private (Expert only)
 */
router.put('/availability/:availabilityId', [
  auth,
  checkPermission('/expert/availabilities', 'UPDATE'),
  body('dayOfWeek').optional().isInt({ min: 1, max: 7 }),
  body('startTime').optional().isString(),
  body('endTime').optional().isString(),
  body('isRecurring').optional().isBoolean(),
  body('specificDate').optional().custom(value => {
    // null değerini kabul et
    if (value === null) return true;
    // ISO 8601 formatını kontrol et
    if (!/^\d{4}-\d{2}-\d{2}(T.*)?$/.test(value)) {
      throw new Error('Geçersiz tarih formatı. ISO 8601 formatında bir tarih olmalı.');
    }
    return true;
  })
], expertsController.updateExpertAvailability);

/**
 * @route   DELETE /api/experts/availability/:availabilityId
 * @desc    Delete availability slot
 * @access  Private (Expert only)
 */
router.delete('/availability/:availabilityId', 
  auth, 
  checkPermission('/expert/availabilities', 'DELETE'),
  expertsController.deleteExpertAvailability
);

// Root path route
/**
 * @route   GET /api/experts
 * @desc    Get all active and verified experts
 * @access  Public
 */
router.get('/', expertsController.getAllExperts);

// ========================
// DINAMIK ROTALAR - SONDA TANIMLANMALI
// ========================

/**
 * @route   GET /api/experts/:id/available-slots
 * @desc    Get expert available time slots (excluding confirmed appointments)
 * @access  Public
 */
router.get('/:id/available-slots', expertsController.getExpertAvailableSlots);

/**
 * @route   GET /api/experts/:id
 * @desc    Get expert by ID
 * @access  Public
 */
router.get('/:id', expertsController.getExpertById);

/**
 * @route   GET /api/experts/:id/availability
 * @desc    Get expert availability
 * @access  Public
 */
router.get('/:id/availability', expertsController.getExpertAvailability);

/**
 * @route   GET /api/experts/:id/categories
 * @desc    Get expert categories
 * @access  Public/Private
 */
router.get('/:id/categories', expertsController.getExpertCategories);

/**
 * @route   PUT /api/experts/:id/categories
 * @desc    Update expert categories
 * @access  Private (Expert only)
 */
router.put('/:id/categories', [
  auth,
  body('categories').isArray().withMessage('Kategoriler bir dizi olmalıdır')
], expertsController.updateExpertCategories);

/**
 * @route   GET /api/experts/:id/languages
 * @desc    Get expert languages
 * @access  Public/Private
 */
router.get('/:id/languages', expertsController.getExpertLanguages);

/**
 * @route   PUT /api/experts/:id/languages
 * @desc    Update expert languages
 * @access  Private (Expert only)
 */
router.put('/:id/languages', [
  auth,
  body('languages').isArray().withMessage('Diller bir dizi olmalıdır')
], expertsController.updateExpertLanguages);

/**
 * @route   GET /api/experts/:id/specializations
 * @desc    Get expert specializations
 * @access  Public/Private
 */
router.get('/:id/specializations', expertsController.getExpertSpecializations);

/**
 * @route   PUT /api/experts/:id/specializations
 * @desc    Update expert specializations
 * @access  Private (Expert only)
 */
router.put('/:id/specializations', [
  auth,
  body('specializations').isArray().withMessage('Uzmanlık alanları bir dizi olmalıdır')
], expertsController.updateExpertSpecializations);

/**
 * @route   GET /api/experts/test-google-calendar
 * @desc    Test Google Calendar service
 * @access  Private (Expert only)
 */
router.get('/test-google-calendar', auth, expertsController.testGoogleCalendar);

module.exports = router;