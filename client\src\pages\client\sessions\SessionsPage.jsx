import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { Link } from 'react-router-dom';
import {
  VideoCameraIcon,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  CheckCircleIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  StarIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';
import api from '../../../services/api';
import toast from 'react-hot-toast';

/**
 * <PERSON><PERSON><PERSON><PERSON> seansları sayfası
 */
const SessionsPage = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [sessions, setSessions] = useState([]);
  const [activeTab, setActiveTab] = useState('upcoming'); // upcoming, past, all

  // Seans durumları
  const sessionStatuses = {
    scheduled: "Planlandı",
    inProgress: "Devam Ediyor", 
    completed: "Tamamlandı",
    missed: "Kaçırıldı",
    cancelled: "İptal Edildi",
  };

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Client Sessions: API çağrısı başlatılıyor...');

      // Client appointments'ları al (confirmed olanlar sessions olarak gösterilecek)
      const response = await api.get('/clients/appointments');
      console.log('📡 Client Sessions: API response:', response.data);

      const appointments = response.data.appointments || [];
      console.log('📋 Client Sessions: Appointments:', appointments);

      // Sadece confirmed appointments'ları sessions olarak göster
      const confirmedSessions = appointments
        .filter(apt => {
          console.log(`🔍 Filtering appointment ${apt.id}: status = ${apt.status}`);
          return apt.status === 'confirmed' || apt.status === 'Confirmed';
        })
        .map(apt => {
          console.log('🔄 Mapping appointment:', apt);
          return {
            ...apt,
            sessionStatus: getSessionStatus(apt),
            canJoinMeeting: canJoinMeeting(apt.appointmentDate),
            isUpcoming: new Date(apt.appointmentDate) > new Date(),
            isPast: new Date(apt.appointmentDate) < new Date()
          };
        });

      console.log('✅ Client Sessions: Confirmed sessions:', confirmedSessions);
      setSessions(confirmedSessions);
    } catch (error) {
      console.error('❌ Client Sessions: Hata:', error);
      toast.error('Seanslar yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  };

  // Seans durumunu belirle
  const getSessionStatus = (appointment) => {
    const now = new Date();
    const appointmentTime = new Date(appointment.appointmentDate);
    const endTime = new Date(appointment.endTime);
    
    if (now < appointmentTime) {
      return 'scheduled'; // Planlandı
    } else if (now >= appointmentTime && now <= endTime) {
      return 'inProgress'; // Devam ediyor
    } else {
      return 'completed'; // Tamamlandı
    }
  };

  // Meeting'e katılabilir mi kontrol et (TEST MODU)
  const canJoinMeeting = (appointmentDate) => {
    // TEST MODU: Onaylandığı andan itibaren katılabilir
    return true;

    /* PRODUCTION MODU (test sonrası aktifleştir):
    const now = new Date();
    const appointmentTime = new Date(appointmentDate);
    const timeDiff = appointmentTime.getTime() - now.getTime();

    // 15 dakika öncesinden 1 saat sonrasına kadar katılabilir
    return timeDiff <= 15 * 60 * 1000 && timeDiff >= -60 * 60 * 1000;
    */
  };

  // Status badge renkleri
  const getStatusBadge = (status) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'inProgress':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'missed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Filtrelenmiş seanslar
  const filteredSessions = sessions.filter(session => {
    if (activeTab === 'upcoming') {
      return session.isUpcoming;
    } else if (activeTab === 'past') {
      return session.isPast;
    }
    return true; // all
  });

  // İstatistikler
  const stats = {
    total: sessions.length,
    upcoming: sessions.filter(s => s.isUpcoming).length,
    completed: sessions.filter(s => s.sessionStatus === 'completed').length,
    missed: sessions.filter(s => s.sessionStatus === 'missed').length
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">Seanslarım</h1>
                <p className="text-blue-100 mt-1">Psikolojik danışmanlık seanslarınızı yönetin</p>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">{stats.total}</div>
                <div className="text-blue-100 text-sm">Toplam Seans</div>
              </div>
            </div>
          </div>
        </div>

        {/* İstatistikler */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white shadow rounded-lg p-4 border-l-4 border-blue-500">
            <div className="flex items-center">
              <CalendarIcon className="h-8 w-8 text-blue-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Yaklaşan</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.upcoming}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white shadow rounded-lg p-4 border-l-4 border-green-500">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Tamamlanan</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.completed}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white shadow rounded-lg p-4 border-l-4 border-red-500">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Kaçırılan</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.missed}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white shadow rounded-lg p-4 border-l-4 border-purple-500">
            <div className="flex items-center">
              <StarIcon className="h-8 w-8 text-purple-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Toplam</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Sekme Navigasyonu */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('upcoming')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'upcoming'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <CalendarIcon className="h-5 w-5 mr-2" />
                  <span>Yaklaşan Seanslar ({stats.upcoming})</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('past')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'past'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 mr-2" />
                  <span>Geçmiş Seanslar ({sessions.length - stats.upcoming})</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('all')}
                className={`py-4 px-6 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'all'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <StarIcon className="h-5 w-5 mr-2" />
                  <span>Tüm Seanslar ({stats.total})</span>
                </div>
              </button>
            </nav>
          </div>

          {/* Seanslar Listesi */}
          <div className="divide-y divide-gray-200">
            {filteredSessions.length === 0 ? (
              <div className="p-8 text-center">
                <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Seans bulunamadı</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {activeTab === 'upcoming' ? 'Yaklaşan seansınız bulunmuyor.' : 'Bu kategoride seans bulunmuyor.'}
                </p>
                {activeTab === 'upcoming' && (
                  <div className="mt-6">
                    <Link
                      to="/client/experts"
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      Uzman Bul
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              filteredSessions.map((session) => (
                <div key={session.id} className="p-6 hover:bg-gray-50 transition duration-150">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          className="h-10 w-10 rounded-full border border-gray-200"
                          src={session.expertAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.expertName)}&background=random&size=40`}
                          alt={session.expertName}
                        />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-900">{session.expertName}</h3>
                        <div className="flex space-x-2 text-xs text-gray-500">
                          <span>{format(parseISO(session.appointmentDate), 'EEEE', { locale: tr })}</span>
                          <span>•</span>
                          <span>{format(parseISO(session.appointmentDate), 'd MMMM yyyy', { locale: tr })}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadge(session.sessionStatus)}`}>
                        {sessionStatuses[session.sessionStatus]}
                      </span>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex space-x-6 text-sm text-gray-500">
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>
                          {format(parseISO(session.appointmentDate), 'HH:mm')} - {format(parseISO(session.endTime), 'HH:mm')}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <UserIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                        <span>Randevu #{session.id}</span>
                      </div>
                      {session.notes && (
                        <div className="flex items-center">
                          <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-1.5" />
                          <span className="truncate max-w-32">{session.notes}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      {/* Google Meet Katılma Butonu */}
                      {session.meetingLink && session.canJoinMeeting && (
                        <button
                          type="button"
                          onClick={() => window.open(session.meetingLink, '_blank')}
                          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <VideoCameraIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Görüşmeye Katıl
                        </button>
                      )}
                      
                      {/* Meeting Link Hazır Ama Henüz Katılamaz */}
                      {session.meetingLink && !session.canJoinMeeting && session.sessionStatus === 'scheduled' && (
                        <div className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-500 bg-gray-50">
                          <VideoCameraIcon className="-ml-0.5 mr-1 h-4 w-4" />
                          Görüşme Hazır
                        </div>
                      )}
                      
                      {/* Mesaj Gönder */}
                      <Link
                        to={`/client/messages?expertId=${session.expertId}`}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <ChatBubbleLeftRightIcon className="-ml-0.5 mr-1 h-4 w-4" />
                        Mesaj
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionsPage;
