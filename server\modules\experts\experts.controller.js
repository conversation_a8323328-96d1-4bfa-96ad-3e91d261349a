/**
 * Experts Controller
 * Handles expert-related API requests
 */

const { validationResult } = require('express-validator');
const expertsService = require('./experts.service');
const usersService = require('../users/users.service');
const notificationsService = require('../notifications/notifications.service');
const clientsService = require('../clients/clients.service');
const googleCalendarService = require('../../services/google-calendar.service');
const logger = require('../../utils/logger');
const fs = require('fs');
const path = require('path');

/**
 * Delete old profile image file
 * @param {string} imagePath - Path to the image file
 */
const deleteOldImage = (imagePath) => {
  if (!imagePath || !imagePath.startsWith('/uploads/')) return;

  const fullPath = path.join(__dirname, '../../', imagePath);

  fs.unlink(fullPath, (err) => {
    if (err && err.code !== 'ENOENT') {
      logger.error('Error deleting old profile image:', err);
    } else {
      logger.info('Old profile image deleted:', imagePath);
    }
  });
};

/**
 * Get all experts
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAllExperts = async (req, res) => {
  try {
    const experts = await expertsService.getAllExperts();
    
    // Map to response format
    const formattedExperts = experts.map(expert => ({
      id: expert.id,
      userId: expert.UserID,
      firstName: expert.firstName,
      lastName: expert.lastName,
      specialty: expert.specialty,
      biography: expert.biography,
      education: expert.education,
      certificates: expert.certificates,
      shortBio: expert.shortBio,
      experienceYears: expert.experienceYears,
      hourlyRate: expert.hourlyRate,
      isVerified: expert.IsVerified,
      rating: expert.rating,
      reviewCount: expert.reviewCount,
      profileImage: expert.profileImage,
      specialties: expert.specialties
    }));
    
    res.json({ experts: formattedExperts });
  } catch (error) {
    logger.error('Error getting all experts:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert by ID
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertById = async (req, res) => {
  try {
    const expertId = parseInt(req.params.id);
    
    // Validate expert ID
    if (isNaN(expertId)) {
      return res.status(400).json({ message: 'Invalid expert ID' });
    }
    
    const expert = await expertsService.getExpertById(expertId);
    
    // Check if expert exists
    if (!expert) {
      return res.status(404).json({ message: 'Expert not found' });
    }
    
    // Map to response format
    const formattedExpert = {
      id: expert.ExpertID,
      userId: expert.UserID,
      firstName: expert.FirstName,
      lastName: expert.LastName,
      email: expert.Email,
      phoneNumber: expert.PhoneNumber,
      specialty: expert.Specialty,
      biography: expert.Biography,
      education: expert.Education,
      certificates: expert.Certificates,
      shortBio: expert.ShortBio,
      experienceYears: expert.ExperienceYears,
      hourlyRate: expert.HourlyRate,
      isVerified: expert.IsVerified,
      rating: expert.Rating,
      reviewCount: expert.ReviewCount,
      isActive: expert.IsActive,
      profileImage: expert.ProfileImage,
      responseTime: expert.ResponseTime,
      sessionDuration: expert.SessionDuration,
      locationCity: expert.LocationCity,
      locationCountry: expert.LocationCountry
    };
    
    res.json(formattedExpert);
  } catch (error) {
    logger.error('Error getting expert by ID:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert profile (for logged-in expert)
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertProfile = async (req, res) => {
  try {
    // Get the logged-in user ID
    const userId = req.user.id;
    
    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);
    
    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }
    
    // Map to response format
    const formattedExpert = {
      id: expert.ExpertID,
      userId: expert.UserID,
      firstName: expert.FirstName,
      lastName: expert.LastName,
      email: expert.Email,
      phoneNumber: expert.PhoneNumber,
      specialty: expert.Specialty,
      biography: expert.Biography,
      education: expert.Education,
      certificates: expert.Certificates,
      shortBio: expert.ShortBio,
      experienceYears: expert.ExperienceYears,
      hourlyRate: expert.HourlyRate,
      isVerified: expert.IsVerified,
      rating: expert.Rating,
      reviewCount: expert.ReviewCount,
      isActive: expert.IsActive,
      profileVisibility: expert.ProfileVisibility,
      responseTime: expert.ResponseTime,
      sessionDuration: expert.SessionDuration,
      profileImage: expert.ProfileImage,
      videoIntro: expert.VideoIntro,
      locationAddress: expert.LocationAddress,
      locationCity: expert.LocationCity,
      locationCountry: expert.LocationCountry
    };
    
    res.json(formattedExpert);
  } catch (error) {
    logger.error('Error getting expert profile:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update expert profile
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const updateExpertProfile = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Get the logged-in user ID
    const userId = req.user.id;

    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);

    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profili bulunamadı' });
    }

    // Profil fotoğrafı değişiklik kontrolü
    if (expert.profileImage) {
      // Eğer profil fotoğrafı kaldırılıyorsa (boş string)
      if (req.body.profileImage === '' || req.body.profileImage === null) {
        deleteOldImage(expert.profileImage);
      }
      // Eğer yeni profil fotoğrafı varsa ve eskisinden farklıysa
      else if (req.body.profileImage &&
               req.body.profileImage !== expert.profileImage &&
               req.body.profileImage.startsWith('/uploads/')) {
        deleteOldImage(expert.profileImage);
      }
    }

    // Prepare expert update data
    const expertUpdateData = {
      specialty: req.body.specialty,
      biography: req.body.biography,
      education: req.body.education,
      certificates: req.body.certificates,
      shortBio: req.body.shortBio,
      experienceYears: req.body.experienceYears,
      hourlyRate: req.body.hourlyRate,
      // Yeni alanlar
      profileVisibility: req.body.profileVisibility,
      responseTime: req.body.responseTime,
      sessionDuration: req.body.sessionDuration,
      profileImage: req.body.profileImage,
      videoIntro: req.body.videoIntro,
      locationAddress: req.body.locationAddress,
      locationCity: req.body.locationCity,
      locationCountry: req.body.locationCountry
    };

    // Update expert profile
    await expertsService.updateExpertProfile(expert.ExpertID, expertUpdateData);

    // Kullanıcı bilgilerini de güncelle (firstName, lastName, email)
    if (req.body.firstName || req.body.lastName || req.body.email) {
      const userUpdateData = {};
      if (req.body.firstName) userUpdateData.firstName = req.body.firstName;
      if (req.body.lastName) userUpdateData.lastName = req.body.lastName;
      if (req.body.email) userUpdateData.email = req.body.email;

      await usersService.updateUserProfile(userId, userUpdateData);
    }

    // Güncellenmiş profili getir
    const updatedExpert = await expertsService.getExpertByUserId(userId);

    res.json({
      message: 'Profil başarıyla güncellendi',
      expert: updatedExpert
    });
  } catch (error) {
    logger.error('Expert profil güncelleme hatası:', error);
    res.status(500).json({ message: 'Profil güncellenirken bir hata oluştu' });
  }
};

/**
 * Upload expert profile image
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const uploadProfileImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'Dosya yüklenmedi' });
    }

    const userId = req.user.id;
    const expert = await expertsService.getExpertByUserId(userId);

    if (!expert) {
      return res.status(404).json({ message: 'Expert profili bulunamadı' });
    }

    // Eski profil fotoğrafını sil
    if (expert.profileImage) {
      deleteOldImage(expert.profileImage);
    }

    // File path to store in database (relative path)
    const profileImagePath = `/uploads/profile-images/${req.file.filename}`;

    // Update expert profile with new image path
    await expertsService.updateExpertProfile(expert.ExpertID, {
      profileImage: profileImagePath
    });

    res.json({
      message: 'Profil fotoğrafı başarıyla yüklendi',
      profileImage: profileImagePath
    });
  } catch (error) {
    logger.error('Error uploading profile image:', error);
    res.status(500).json({ message: 'Dosya yüklenirken bir hata oluştu' });
  }
};

/**
 * Get all specialties
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAllSpecialties = async (req, res) => {
  try {
    const specialties = await expertsService.getAllSpecialties();
    res.json({ specialties });
  } catch (error) {
    logger.error('Error getting specialties:', error);
    res.status(500).json({ message: 'Uzmanlık alanları alınırken bir hata oluştu' });
  }
};

/**
 * Get expert specialties
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertSpecialties = async (req, res) => {
  try {
    const userId = req.user.id;
    const expert = await expertsService.getExpertByUserId(userId);

    if (!expert) {
      return res.status(404).json({ message: 'Expert profili bulunamadı' });
    }

    const specialties = await expertsService.getExpertSpecialties(expert.ExpertID);
    res.json({ specialties });
  } catch (error) {
    logger.error('Error getting expert specialties:', error);
    res.status(500).json({ message: 'Expert uzmanlık alanları alınırken bir hata oluştu' });
  }
};

/**
 * Update expert specialties
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const updateExpertSpecialties = async (req, res) => {
  try {
    const userId = req.user.id;
    const { specialtyIds } = req.body;

    if (!Array.isArray(specialtyIds)) {
      return res.status(400).json({ message: 'Uzmanlık alanları dizi formatında olmalıdır' });
    }

    const expert = await expertsService.getExpertByUserId(userId);

    if (!expert) {
      return res.status(404).json({ message: 'Expert profili bulunamadı' });
    }

    await expertsService.updateExpertSpecialties(expert.ExpertID, specialtyIds);

    const updatedSpecialties = await expertsService.getExpertSpecialties(expert.ExpertID);

    res.json({
      message: 'Uzmanlık alanları başarıyla güncellendi',
      specialties: updatedSpecialties
    });
  } catch (error) {
    logger.error('Error updating expert specialties:', error);
    res.status(500).json({ message: 'Uzmanlık alanları güncellenirken bir hata oluştu' });
  }
};

/**
 * Get expert availability
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertAvailability = async (req, res) => {
  try {
    // Get expert ID from params or from logged-in user
    let expertId;
    
    if (req.params.id) {
      expertId = parseInt(req.params.id);
    } else {
      // Get the logged-in user ID
      const userId = req.user.id;
      
      // Get expert profile
      const expert = await expertsService.getExpertByUserId(userId);
      
      // Check if user is an expert
      if (!expert) {
        return res.status(404).json({ message: 'Expert profile not found' });
      }
      
      expertId = expert.ExpertID;
    }
    
    // Validate expert ID
    if (isNaN(expertId)) {
      return res.status(400).json({ message: 'Invalid expert ID' });
    }
    
    // Get availability
    const availability = await expertsService.getExpertAvailability(expertId);
    
    // Map to response format
    const formattedAvailability = availability.map(slot => ({
      id: slot.AvailabilityID,
      dayOfWeek: slot.DayOfWeek,
      startTime: slot.StartTime,
      endTime: slot.EndTime,
      isRecurring: slot.IsRecurring,
      specificDate: slot.SpecificDate
    }));

    res.json({ availability: formattedAvailability });
  } catch (error) {
    logger.error('Error getting expert availability:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Add expert availability slot
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const addExpertAvailability = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Validation errors:', errors.array());
      return res.status(400).json({ errors: errors.array() });
    }
    
    // Get the logged-in user ID
    const userId = req.user.id;
    
    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);
    
    // Check if user is an expert
    if (!expert) {
      logger.error('Expert profile not found for user:', userId);
      return res.status(404).json({ message: 'Expert profile not found' });
    }
    
    // Prepare availability data
    const availabilityData = {
      expertId: expert.ExpertID,
      dayOfWeek: req.body.dayOfWeek,
      startTime: req.body.startTime,
      endTime: req.body.endTime,
      isRecurring: req.body.isRecurring,
      specificDate: req.body.specificDate
    };
    
    logger.info('Adding availability with data:', availabilityData);
    
    // Add availability
    const availabilityId = await expertsService.addExpertAvailability(availabilityData);
    
    logger.info('Availability added successfully with ID:', availabilityId);
    
    res.status(201).json({
      id: availabilityId,
      message: 'Availability added successfully'
    });
  } catch (error) {
    logger.error('Error adding expert availability:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update expert availability slot
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const updateExpertAvailability = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Validation errors:', errors.array());
      return res.status(400).json({ errors: errors.array() });
    }
    
    const availabilityId = parseInt(req.params.availabilityId);
    
    // Validate availability ID
    if (isNaN(availabilityId)) {
      logger.error('Invalid availability ID:', req.params.availabilityId);
      return res.status(400).json({ message: 'Invalid availability ID' });
    }
    
    // Get the logged-in user ID
    const userId = req.user.id;
    
    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);
    
    // Check if user is an expert
    if (!expert) {
      logger.error('Expert profile not found for user:', userId);
      return res.status(404).json({ message: 'Expert profile not found' });
    }
    
    // Önce müsaitlik kaydının mevcut ve uzmana ait olup olmadığını kontrol et
    const availabilityCheck = await expertsService.getAvailabilityById(availabilityId);
    
    if (!availabilityCheck) {
      logger.error(`Availability record with ID ${availabilityId} not found`);
      return res.status(404).json({ message: 'Availability slot not found' });
    }
    
    if (availabilityCheck.ExpertID !== expert.ExpertID) {
      logger.error(`Availability record with ID ${availabilityId} belongs to ExpertID ${availabilityCheck.ExpertID}, not to current expert (${expert.ExpertID})`);
      return res.status(403).json({ message: 'You do not have permission to modify this availability slot' });
    }
    
    // Prepare availability data
    const availabilityData = {
      dayOfWeek: req.body.dayOfWeek,
      startTime: req.body.startTime,
      endTime: req.body.endTime,
      isRecurring: req.body.isRecurring,
      specificDate: req.body.specificDate
    };
    
    logger.info('Updating availability with data:', {
      availabilityId,
      expertId: expert.ExpertID,
      ...availabilityData
    });
    
    try {
      // Update availability
      const updated = await expertsService.updateExpertAvailability(availabilityId, availabilityData);
      
      if (!updated) {
        logger.error('Availability slot not found:', availabilityId);
        return res.status(404).json({ message: 'Availability slot not found' });
      }
      
      logger.info('Availability updated successfully:', availabilityId);
      res.json({ message: 'Availability updated successfully' });
    } catch (serviceError) {
      logger.error('Error in expert service while updating availability:', {
        error: serviceError.message,
        stack: serviceError.stack,
        availabilityId,
        availabilityData
      });
      return res.status(500).json({ message: 'Error updating availability', error: serviceError.message });
    }
  } catch (error) {
    logger.error('Error updating expert availability:', error);
    res.status(500).json({ message: 'Server error', details: error.message });
  }
};

/**
 * Delete expert availability slot
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const deleteExpertAvailability = async (req, res) => {
  try {
    const availabilityId = parseInt(req.params.availabilityId);
    
    // Validate availability ID
    if (isNaN(availabilityId)) {
      return res.status(400).json({ message: 'Invalid availability ID' });
    }
    
    // Delete availability
    const deleted = await expertsService.deleteExpertAvailability(availabilityId);
    
    if (!deleted) {
      return res.status(404).json({ message: 'Availability slot not found' });
    }
    
    res.json({ message: 'Availability deleted successfully' });
  } catch (error) {
    logger.error('Error deleting expert availability:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

const addRecurringAvailability = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    // Get the logged-in user ID
    const userId = req.user.id;
    
    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);
    
    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }
    
    const { daysOfWeek, startTime, endTime } = req.body;
    
    // Validate time range
    if (startTime >= endTime) {
      return res.status(400).json({ message: 'End time must be after start time' });
    }
    
    // Add availability for each day
    const results = [];
    
    for (const dayOfWeek of daysOfWeek) {
      // Prepare availability data
      const availabilityData = {
        expertId: expert.ExpertID,
        dayOfWeek,
        startTime,
        endTime,
        isRecurring: true,
        specificDate: null
      };
      
      // Add availability
      const availabilityId = await expertsService.addExpertAvailability(availabilityData);
      results.push({ dayOfWeek, availabilityId });
    }
    
    res.status(201).json({
      message: 'Recurring availability added successfully',
      results
    });
  } catch (error) {
    logger.error('Error adding recurring expert availability:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

const addSpecificDateAvailability = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    // Get the logged-in user ID
    const userId = req.user.id;
    
    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);
    
    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }
    
    const { date, startTime, endTime } = req.body;
    
    // Validate time range
    if (startTime >= endTime) {
      return res.status(400).json({ message: 'End time must be after start time' });
    }
    
    // Convert date to day of week (1-7, Monday is 1)
    const dateObj = new Date(date);
    const dayOfWeek = dateObj.getDay() === 0 ? 7 : dateObj.getDay();
    
    // Prepare availability data
    const availabilityData = {
      expertId: expert.ExpertID,
      dayOfWeek,
      startTime,
      endTime,
      isRecurring: false,
      specificDate: date
    };
    
    // Add availability
    const availabilityId = await expertsService.addExpertAvailability(availabilityData);
    
    res.status(201).json({
      id: availabilityId,
      message: 'Specific date availability added successfully'
    });
  } catch (error) {
    logger.error('Error adding specific date expert availability:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

const getAvailabilityByDateRange = async (req, res) => {
  try {
    // Get query parameters
    const { startDate, endDate } = req.query;
    
    // Validate dates
    if (!startDate || !endDate) {
      return res.status(400).json({ message: 'Both startDate and endDate are required' });
    }
    
    // Check if dates are valid
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }
    
    // Get the logged-in user ID
    const userId = req.user.id;
    
    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);
    
    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }
    
    // Get availability for date range
    const availability = await expertsService.getExpertAvailabilityByDateRange(
      expert.ExpertID, startDate, endDate
    );
    
    // Map to response format
    const formattedAvailability = availability.map(slot => ({
      id: slot.AvailabilityID,
      date: slot.SpecificDate,
      dayOfWeek: slot.DayOfWeek,
      startTime: slot.StartTime,
      endTime: slot.EndTime,
      isRecurring: slot.IsRecurring
    }));
    
    res.json(formattedAvailability);
  } catch (error) {
    logger.error('Error getting expert availability by date range:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get all categories
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAllCategories = async (req, res) => {
  try {
    const categories = await expertsService.getAllCategories();
    res.json(categories);
  } catch (error) {
    logger.error('Error getting all categories:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get all languages
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAllLanguages = async (req, res) => {
  try {
    const languages = await expertsService.getAllLanguages();
    res.json(languages);
  } catch (error) {
    logger.error('Error getting all languages:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get all specializations
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAllSpecializations = async (req, res) => {
  try {
    const specializations = await expertsService.getAllSpecializations();
    res.json(specializations);
  } catch (error) {
    logger.error('Error getting all specializations:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert categories
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertCategories = async (req, res) => {
  try {
    let expertId;
    
    if (req.params.id) {
      expertId = parseInt(req.params.id);
    } else {
      // Eğer ID parametre olarak verilmemişse, giriş yapan kullanıcının ID'sini al
      const userId = req.user.id;
      const expert = await expertsService.getExpertByUserId(userId);
      
      if (!expert) {
        return res.status(404).json({ message: 'Expert profile not found' });
      }
      
      expertId = expert.ExpertID;
    }
    
    const categories = await expertsService.getExpertCategories(expertId);
    res.json(categories);
  } catch (error) {
    logger.error('Error getting expert categories:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update expert categories
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const updateExpertCategories = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    let expertId;
    
    if (req.params.id) {
      expertId = parseInt(req.params.id);
    } else {
      // Eğer ID parametre olarak verilmemişse, giriş yapan kullanıcının ID'sini al
      const userId = req.user.id;
      const expert = await expertsService.getExpertByUserId(userId);
      
      if (!expert) {
        return res.status(404).json({ message: 'Expert profile not found' });
      }
      
      expertId = expert.ExpertID;
    }
    
    const categoryIds = req.body.categories || [];
    const updated = await expertsService.updateExpertCategories(expertId, categoryIds);
    
    if (!updated) {
      return res.status(500).json({ message: 'Failed to update categories' });
    }
    
    res.json({ message: 'Categories updated successfully' });
  } catch (error) {
    logger.error('Error updating expert categories:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert languages
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertLanguages = async (req, res) => {
  try {
    let expertId;
    
    if (req.params.id) {
      expertId = parseInt(req.params.id);
    } else {
      // Eğer ID parametre olarak verilmemişse, giriş yapan kullanıcının ID'sini al
      const userId = req.user.id;
      const expert = await expertsService.getExpertByUserId(userId);
      
      if (!expert) {
        return res.status(404).json({ message: 'Expert profile not found' });
      }
      
      expertId = expert.ExpertID;
    }
    
    const languages = await expertsService.getExpertLanguages(expertId);
    res.json(languages);
  } catch (error) {
    logger.error('Error getting expert languages:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update expert languages
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const updateExpertLanguages = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    let expertId;
    
    if (req.params.id) {
      expertId = parseInt(req.params.id);
    } else {
      // Eğer ID parametre olarak verilmemişse, giriş yapan kullanıcının ID'sini al
      const userId = req.user.id;
      const expert = await expertsService.getExpertByUserId(userId);
      
      if (!expert) {
        return res.status(404).json({ message: 'Expert profile not found' });
      }
      
      expertId = expert.ExpertID;
    }
    
    const languages = req.body.languages || [];
    const updated = await expertsService.updateExpertLanguages(expertId, languages);
    
    if (!updated) {
      return res.status(500).json({ message: 'Failed to update languages' });
    }
    
    res.json({ message: 'Languages updated successfully' });
  } catch (error) {
    logger.error('Error updating expert languages:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert specializations
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertSpecializations = async (req, res) => {
  try {
    let expertId;
    
    if (req.params.id) {
      expertId = parseInt(req.params.id);
    } else {
      // Eğer ID parametre olarak verilmemişse, giriş yapan kullanıcının ID'sini al
      const userId = req.user.id;
      const expert = await expertsService.getExpertByUserId(userId);
      
      if (!expert) {
        return res.status(404).json({ message: 'Expert profile not found' });
      }
      
      expertId = expert.ExpertID;
    }
    
    const specializations = await expertsService.getExpertSpecializations(expertId);
    res.json(specializations);
  } catch (error) {
    logger.error('Error getting expert specializations:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update expert specializations
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const updateExpertSpecializations = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    let expertId;
    
    if (req.params.id) {
      expertId = parseInt(req.params.id);
    } else {
      // Eğer ID parametre olarak verilmemişse, giriş yapan kullanıcının ID'sini al
      const userId = req.user.id;
      const expert = await expertsService.getExpertByUserId(userId);
      
      if (!expert) {
        return res.status(404).json({ message: 'Expert profile not found' });
      }
      
      expertId = expert.ExpertID;
    }
    
    const specializationIds = req.body.specializations || [];
    const updated = await expertsService.updateExpertSpecializations(expertId, specializationIds);
    
    if (!updated) {
      return res.status(500).json({ message: 'Failed to update specializations' });
    }
    
    res.json({ message: 'Specializations updated successfully' });
  } catch (error) {
    logger.error('Error updating expert specializations:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert appointments
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertAppointments = async (req, res) => {
  try {
    // Get the logged-in user ID
    const userId = req.user.id;

    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);

    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }

    // Get appointments
    const appointments = await expertsService.getExpertAppointments(expert.ExpertID);

    res.json({ appointments });
  } catch (error) {
    logger.error('Error getting expert appointments:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update appointment status
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const updateAppointmentStatus = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const appointmentId = parseInt(req.params.id);
    const { status, rejectionReason } = req.body;

    // Get the logged-in user ID
    const userId = req.user.id;

    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);

    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }

    // Get appointment details before updating (for notification)
    const appointmentDetail = await expertsService.getAppointmentDetail(appointmentId, expert.ExpertID);

    if (!appointmentDetail) {
      return res.status(404).json({ message: 'Appointment not found or not authorized' });
    }

    // Update appointment status
    const updated = await expertsService.updateAppointmentStatus(
      appointmentId,
      expert.ExpertID,
      status,
      rejectionReason
    );

    if (!updated) {
      return res.status(404).json({ message: 'Appointment not found or not authorized' });
    }

    // Eğer randevu onaylandıysa Google Meet linki oluştur
    if (status === 'Confirmed') {
      try {
        console.log(`🔄 Randevu ${appointmentId} onaylandı, Google Meet linki oluşturuluyor...`);
        logger.info(`🔄 Randevu ${appointmentId} onaylandı, Google Meet linki oluşturuluyor...`);

        console.log('👤 Expert user bilgisi alınıyor...');
        const expert_user = await usersService.getUserById(userId);
        console.log('✅ Expert user alındı:', {
          id: expert_user.id,
          firstName: expert_user.firstName,
          lastName: expert_user.lastName,
          email: expert_user.email
        });

        // Google Meet linki oluştur
        console.log('🔍 Google Calendar service availability kontrol ediliyor...');
        if (googleCalendarService.isAvailable()) {
          console.log('📅 Google Calendar service mevcut, meeting oluşturuluyor...');
          logger.info('📅 Google Calendar service mevcut, meeting oluşturuluyor...');

          const appointmentDataForMeeting = {
            id: appointmentId,
            clientName: appointmentDetail.clientName,
            clientEmail: appointmentDetail.clientEmail,
            expertName: `${expert_user.firstName} ${expert_user.lastName}`,
            expertEmail: expert_user.email,
            startTime: appointmentDetail.appointmentDate,
            endTime: appointmentDetail.endTime,
            notes: appointmentDetail.notes
          };
          console.log('📋 Meeting için appointment data:', appointmentDataForMeeting);

          console.log('🚀 Google Calendar createMeetingLink çağrılıyor...');
          const meetingData = await googleCalendarService.createMeetingLink(appointmentDataForMeeting);

          console.log('🎯 Google Meet data alındı:', meetingData);
          logger.info('🎯 Google Meet data alındı:', meetingData);

          // Meeting link'i appointment'a kaydet
          console.log('💾 Database meeting link kaydediliyor...');
          console.log('📝 Kaydedilecek veriler:', {
            appointmentId: appointmentId,
            meetingLink: meetingData.meetingLink,
            eventId: meetingData.eventId
          });

          const updateResult = await expertsService.updateAppointmentMeetingLink(
            appointmentId,
            meetingData.meetingLink,
            meetingData.eventId
          );

          console.log(`💾 Database update result: ${updateResult}`);
          console.log(`✅ Google Meet linki oluşturuldu: ${meetingData.meetingLink}`);
          logger.info(`💾 Database update result: ${updateResult}`);
          logger.info(`✅ Google Meet linki oluşturuldu: ${meetingData.meetingLink}`);
        } else {
          console.log('❌ Google Calendar service not available, skipping meeting link creation');
          logger.warn('❌ Google Calendar service not available, skipping meeting link creation');
        }
      } catch (meetingError) {
        console.error('💥 Google Meet linki oluşturulamadı:', meetingError.message);
        console.error('📝 Meeting error stack:', meetingError.stack);
        logger.error('❌ Google Meet linki oluşturulamadı:', meetingError);
        logger.error('❌ Meeting error stack:', meetingError.stack);
        // Meeting link oluşturulamazsa appointment'ı yine de onayla
      }
    }

    // Create notification for client
    try {
      const expert_user = await usersService.getUserById(userId);

      let notificationTitle, notificationMessage;

      if (status === 'Confirmed') {
        notificationTitle = 'Randevu Onaylandı';
        notificationMessage = `${expert_user.firstName} ${expert_user.lastName} randevunuzu onayladı`;
      } else if (status === 'Rejected') {
        notificationTitle = 'Randevu Reddedildi';
        notificationMessage = `${expert_user.firstName} ${expert_user.lastName} randevunuzu reddetti`;
        if (rejectionReason) {
          notificationMessage += `: ${rejectionReason}`;
        }
      }

      if (notificationTitle && appointmentDetail.clientId) {
        const notificationId = await notificationsService.createNotification({
          userId: appointmentDetail.clientId,
          title: notificationTitle,
          message: notificationMessage,
          type: 'appointment',
          relatedEntityType: 'Appointment',
          relatedEntityId: appointmentId
        });

        console.log('✅ Appointment status notification created:', appointmentId);

        // Emit notification event to client
        const io = req.app.get('io');
        if (io) {
          const notificationData = {
            id: notificationId,
            title: notificationTitle,
            message: notificationMessage,
            type: 'appointment',
            relatedEntityType: 'Appointment',
            relatedEntityId: appointmentId,
            isRead: false,
            timestamp: new Date().toISOString(),
            avatar: 'https://ui-avatars.com/api/?name=Randevu&background=059669&color=fff&size=40'
          };

          io.to(`user_${appointmentDetail.clientId}`).emit('new_notification', notificationData);
          console.log('🔔 Appointment status notification sent to client:', appointmentDetail.clientId);
        }
      }
    } catch (notificationError) {
      logger.error('Error creating appointment status notification:', notificationError);
      // Don't fail the status update if notification fails
    }

    res.json({
      message: `Appointment ${status.toLowerCase()} successfully`,
      status
    });
  } catch (error) {
    logger.error('Error updating appointment status:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get appointment detail
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAppointmentDetail = async (req, res) => {
  try {
    // Get the logged-in user ID
    const userId = req.user.id;
    const appointmentId = parseInt(req.params.id);

    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);

    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }

    // Get appointment detail
    const appointment = await expertsService.getAppointmentDetail(appointmentId, expert.ExpertID);

    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found or not authorized' });
    }

    res.json({ appointment });
  } catch (error) {
    logger.error('Error getting appointment detail:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert sessions
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertSessions = async (req, res) => {
  try {
    // Get the logged-in user ID
    const userId = req.user.id;

    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);

    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }

    // Get sessions
    const sessions = await expertsService.getExpertSessions(expert.ExpertID);

    res.json({ sessions });
  } catch (error) {
    logger.error('Error getting expert sessions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert available time slots (excluding confirmed appointments)
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertAvailableSlots = async (req, res) => {
  try {
    const expertId = parseInt(req.params.id);

    if (!expertId) {
      return res.status(400).json({ message: 'Invalid expert ID' });
    }

    // Get available slots excluding confirmed appointments
    const availableSlots = await expertsService.getExpertAvailableSlots(expertId);

    res.json({ availability: availableSlots });
  } catch (error) {
    logger.error('Error getting expert available slots:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get expert clients
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getExpertClients = async (req, res) => {
  try {
    // Get the logged-in user ID
    const userId = req.user.id;

    // Get expert profile
    const expert = await expertsService.getExpertByUserId(userId);

    // Check if user is an expert
    if (!expert) {
      return res.status(404).json({ message: 'Expert profile not found' });
    }

    // Get clients
    const clients = await expertsService.getExpertClients(expert.ExpertID);

    // Format clients for frontend
    const formattedClients = clients.map(client => ({
      id: client.id,
      firstName: client.FirstName,
      lastName: client.LastName,
      email: client.Email,
      phone: client.phone || 'Belirtilmemiş',
      status: client.status,
      avatar: client.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(client.FirstName + ' ' + client.LastName)}&background=random&size=40`,
      totalSessions: client.totalSessions || 0,
      completedSessions: client.completedSessions || 0,
      lastSession: client.lastSession,
      nextSession: client.nextSession,
      createdAt: client.CreatedAt,
      tags: client.tags ? client.tags.split(',') : ['Genel'],
      package: client.package || 'Standart Paket',
      paymentStatus: client.paymentStatus || 'pending'
    }));

    res.json({ clients: formattedClients });
  } catch (error) {
    logger.error('Error getting expert clients:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getAllExperts,
  getExpertById,
  getExpertProfile,
  updateExpertProfile,
  uploadProfileImage,
  getExpertAvailability,
  addExpertAvailability,
  updateExpertAvailability,
  deleteExpertAvailability,
  addRecurringAvailability,
  addSpecificDateAvailability,
  getAvailabilityByDateRange,
  getAllCategories,
  getAllLanguages,
  getAllSpecializations,
  getAllSpecialties,
  getExpertCategories,
  updateExpertCategories,
  getExpertLanguages,
  updateExpertLanguages,
  getExpertSpecializations,
  updateExpertSpecializations,
  getExpertSpecialties,
  updateExpertSpecialties,
  getExpertAppointments,
  getAppointmentDetail,
  updateAppointmentStatus,
  getExpertSessions,
  getExpertAvailableSlots,
  getExpertClients
};