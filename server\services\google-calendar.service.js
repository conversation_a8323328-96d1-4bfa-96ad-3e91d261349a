const { google } = require('googleapis');
const path = require('path');
const logger = require('../utils/logger');

class GoogleCalendarService {
  constructor() {
    console.log('🏗️  GoogleCalendarService constructor çağrıldı');
    this.calendar = null;
    this.auth = null;
    this.isInitialized = false;
  }

  /**
   * Initialize Google Calendar Service
   * @returns {Promise<boolean>} Success status
   */
  async initialize() {
    try {
      console.log('🔧 Google Calendar Service initialize başlatılıyor...');

      // Service Account JSON dosyasını yükle
      const keyFile = path.join(__dirname, '../config/google-service-account.json');
      console.log('📁 Service account dosya yolu:', keyFile);

      // Dosya var mı kontrol et
      if (!require('fs').existsSync(keyFile)) {
        console.error('❌ Google service account dosyası bulunamadı:', keyFile);
        logger.warn(`Google service account file not found: ${keyFile}`);
        this.calendar = null;
        return false;
      }
      console.log('✅ Service account dosyası bulundu');

      // Google Auth oluştur
      console.log('🔐 Google Auth oluşturuluyor...');
      this.auth = new google.auth.GoogleAuth({
        keyFile: keyFile,
        scopes: [
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events'
        ]
      });
      console.log('✅ Google Auth oluşturuldu');

      // Calendar API client oluştur
      console.log('📅 Calendar API client oluşturuluyor...');
      this.calendar = google.calendar({ version: 'v3', auth: this.auth });
      console.log('✅ Calendar API client oluşturuldu');

      // Test bağlantısı
      console.log('🧪 Google Calendar bağlantısı test ediliyor...');
      const authClient = await this.auth.getClient();
      console.log('✅ Auth client alındı');

      // Basit bir test çağrısı
      await this.calendar.calendarList.list({ maxResults: 1 });
      console.log('✅ Google Calendar API test başarılı');

      this.isInitialized = true;
      logger.info('Google Calendar Service initialized successfully');
      console.log('🎉 Google Calendar Service başarıyla başlatıldı!');
      return true;

    } catch (error) {
      console.error('💥 Google Calendar Service initialize hatası:', error.message);
      console.error('📝 Hata detayları:', error);
      logger.error('Google Calendar Service initialization failed:', error);
      this.calendar = null;
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Create Google Meet link for appointment
   * @param {Object} appointmentData - Appointment details
   * @returns {Promise<Object>} Meeting data with link and event ID
   */
  async createMeetingLink(appointmentData) {
    try {
      console.log('🚀 createMeetingLink çağrıldı, appointmentData:', appointmentData);

      if (!this.calendar) {
        console.error('❌ Google Calendar service not initialized');
        throw new Error('Google Calendar service not initialized');
      }
      console.log('✅ Google Calendar service mevcut');

      const startTime = new Date(appointmentData.startTime);
      const endTime = new Date(appointmentData.endTime);
      console.log('📅 Tarih parsing:', {
        originalStartTime: appointmentData.startTime,
        originalEndTime: appointmentData.endTime,
        parsedStartTime: startTime,
        parsedEndTime: endTime
      });

      // 50 dakikalık seans süresi ayarla
      if (!appointmentData.endTime) {
        endTime.setTime(startTime.getTime() + (50 * 60 * 1000)); // 50 dakika ekle
        console.log('⏰ EndTime otomatik ayarlandı:', endTime);
      }

      const event = {
        summary: `Psikolojik Danışmanlık Seansı - ${appointmentData.clientName}`,
        description: `
🧠 Psikolojik Danışmanlık Seansı

👨‍⚕️ Uzman: ${appointmentData.expertName}
👤 Danışan: ${appointmentData.clientName}
📅 Tarih: ${startTime.toLocaleDateString('tr-TR')}
⏰ Saat: ${startTime.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
⏱️ Süre: 50 dakika

📝 Notlar: ${appointmentData.notes || 'Belirtilmemiş'}

---
Bu görüşme linki sadece randevu katılımcıları için geçerlidir.
Lütfen randevu saatinden 5-10 dakika önce bağlantıya katılın.
        `.trim(),
        start: {
          dateTime: startTime.toISOString(),
          timeZone: 'Europe/Istanbul'
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: 'Europe/Istanbul'
        },
        conferenceData: {
          createRequest: {
            requestId: `appointment-${appointmentData.id}-${Date.now()}`,
            conferenceSolutionKey: { type: 'hangoutsMeet' }
          }
        },
        attendees: [
          { 
            email: appointmentData.expertEmail,
            displayName: appointmentData.expertName,
            responseStatus: 'accepted'
          },
          { 
            email: appointmentData.clientEmail,
            displayName: appointmentData.clientName,
            responseStatus: 'needsAction'
          }
        ],
        reminders: {
          useDefault: false,
          overrides: [
            { method: 'email', minutes: 60 }, // 1 saat önce email
            { method: 'popup', minutes: 15 }  // 15 dakika önce popup
          ]
        },
        visibility: 'private',
        guestsCanModify: false,
        guestsCanInviteOthers: false,
        guestsCanSeeOtherGuests: false
      };

      console.log('📝 Event objesi oluşturuldu:', JSON.stringify(event, null, 2));
      logger.info(`Creating Google Meet for appointment ${appointmentData.id}`);
      console.log('🔄 Google Calendar API çağrısı yapılıyor...');

      const response = await this.calendar.events.insert({
        calendarId: 'primary',
        resource: event,
        conferenceDataVersion: 1,
        sendUpdates: 'all' // Email bildirimi gönder
      });
      console.log('✅ Google Calendar API çağrısı başarılı');
      console.log('📊 API Response data:', {
        id: response.data.id,
        hangoutLink: response.data.hangoutLink,
        htmlLink: response.data.htmlLink,
        status: response.data.status
      });

      const meetingData = {
        meetingLink: response.data.hangoutLink,
        eventId: response.data.id,
        calendarEventLink: response.data.htmlLink,
        startTime: response.data.start.dateTime,
        endTime: response.data.end.dateTime
      };

      console.log('🎉 Meeting data oluşturuldu:', meetingData);
      logger.info(`Google Meet created successfully: ${meetingData.meetingLink}`);
      console.log('✅ createMeetingLink başarıyla tamamlandı');
      return meetingData;

    } catch (error) {
      console.error('💥 createMeetingLink hatası:', error.message);
      console.error('📝 Hata detayları:', error);
      logger.error('Error creating Google Meet link:', error);
      throw new Error(`Google Meet oluşturulamadı: ${error.message}`);
    }
  }

  /**
   * Update existing Google Meet event
   * @param {string} eventId - Google Calendar event ID
   * @param {Object} updates - Updates to apply
   * @returns {Promise<Object>} Updated event data
   */
  async updateMeeting(eventId, updates) {
    try {
      if (!this.calendar) {
        throw new Error('Google Calendar service not initialized');
      }

      const response = await this.calendar.events.patch({
        calendarId: 'primary',
        eventId: eventId,
        resource: updates,
        conferenceDataVersion: 1,
        sendUpdates: 'all'
      });
      
      logger.info(`Google Meet updated: ${eventId}`);
      return response.data;
    } catch (error) {
      logger.error('Error updating Google Meet:', error);
      throw error;
    }
  }

  /**
   * Cancel/Delete Google Meet event
   * @param {string} eventId - Google Calendar event ID
   * @returns {Promise<boolean>} Success status
   */
  async cancelMeeting(eventId) {
    try {
      if (!this.calendar) {
        throw new Error('Google Calendar service not initialized');
      }

      await this.calendar.events.delete({
        calendarId: 'primary',
        eventId: eventId,
        sendUpdates: 'all'
      });
      
      logger.info(`Google Meet cancelled: ${eventId}`);
      return true;
    } catch (error) {
      logger.error('Error cancelling Google Meet:', error);
      throw error;
    }
  }

  /**
   * Check if service is available
   * @returns {boolean} Service availability
   */
  isAvailable() {
    const available = this.calendar !== null && this.isInitialized;
    console.log('🔍 Google Calendar Service availability check:', {
      calendar: this.calendar !== null,
      isInitialized: this.isInitialized,
      available: available
    });
    return available;
  }
}

module.exports = new GoogleCalendarService();
